<?php
/**
 * AstroGenix - Transactions Page
 * Страница истории транзакций пользователя
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

// Параметры фильтрации и сортировки
$type_filter = $_GET['type'] ?? 'all';
$status_filter = $_GET['status'] ?? 'all';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Построение WHERE условий
$where_conditions = ['user_id = ?'];
$params = [$user['id']];

if ($type_filter !== 'all') {
    $where_conditions[] = 'type = ?';
    $params[] = $type_filter;
}

if ($status_filter !== 'all') {
    $where_conditions[] = 'status = ?';
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = 'DATE(created_at) >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'DATE(created_at) <= ?';
    $params[] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// Валидация сортировки
$allowed_sort = ['created_at', 'amount', 'type', 'status'];
$sort_by = in_array($sort_by, $allowed_sort) ? $sort_by : 'created_at';
$sort_order = in_array($sort_order, ['ASC', 'DESC']) ? $sort_order : 'DESC';

try {
    // Получение транзакций с пагинацией
    $stmt = $pdo->prepare("
        SELECT * FROM transactions 
        WHERE {$where_clause}
        ORDER BY {$sort_by} {$sort_order}
        LIMIT {$per_page} OFFSET {$offset}
    ");
    $stmt->execute($params);
    $transactions = $stmt->fetchAll();
    
    // Подсчет общего количества для пагинации
    $count_stmt = $pdo->prepare("
        SELECT COUNT(*) FROM transactions 
        WHERE {$where_clause}
    ");
    $count_stmt->execute($params);
    $total_transactions = $count_stmt->fetchColumn();
    $total_pages = ceil($total_transactions / $per_page);
    
    // Статистика транзакций
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_count,
            COALESCE(SUM(CASE WHEN type = 'deposit' AND status = 'approved' THEN amount ELSE 0 END), 0) as total_deposits,
            COALESCE(SUM(CASE WHEN type = 'withdrawal' AND status = 'approved' THEN amount ELSE 0 END), 0) as total_withdrawals,
            COALESCE(SUM(CASE WHEN type = 'profit' THEN amount ELSE 0 END), 0) as total_profits,
            COALESCE(SUM(CASE WHEN type = 'referral' THEN amount ELSE 0 END), 0) as total_referrals,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count
        FROM transactions 
        WHERE user_id = ?
    ");
    $stats_stmt->execute([$user['id']]);
    $stats = $stats_stmt->fetch();
    
} catch (PDOException $e) {
    $transactions = [];
    $stats = [
        'total_count' => 0,
        'total_deposits' => 0,
        'total_withdrawals' => 0,
        'total_profits' => 0,
        'total_referrals' => 0,
        'pending_count' => 0
    ];
    $total_pages = 1;
}

$page_title = __('transaction_history') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('transaction_history'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
                
                <div class="header-actions">
                    <a href="deposit.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        <?php echo __('deposit'); ?>
                    </a>
                    <a href="withdraw.php" class="btn btn-outline btn-sm">
                        <i class="fas fa-minus"></i>
                        <?php echo __('withdrawal'); ?>
                    </a>
                </div>
            </div>
        </header>
        
        <!-- Transactions Content -->
        <div class="dashboard-content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-arrow-down text-success"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($stats['total_deposits'], 2); ?></div>
                        <div class="stat-label">Всего пополнений</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-arrow-up text-danger"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($stats['total_withdrawals'], 2); ?></div>
                        <div class="stat-label">Всего выводов</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins text-warning"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($stats['total_profits'], 2); ?></div>
                        <div class="stat-label">Прибыль</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users text-info"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($stats['total_referrals'], 2); ?></div>
                        <div class="stat-label">Рефералы</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock text-warning"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $stats['pending_count']; ?></div>
                        <div class="stat-label">В ожидании</div>
                    </div>
                </div>
            </div>

            <!-- Filters and Controls -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Фильтры и поиск</h3>
                </div>
                <div class="card-content">
                    <form method="GET" class="filters-form">
                        <div class="filters-grid">
                            <div class="filter-group">
                                <label for="type">Тип:</label>
                                <select name="type" id="type">
                                    <option value="all" <?php echo $type_filter === 'all' ? 'selected' : ''; ?>>Все типы</option>
                                    <option value="deposit" <?php echo $type_filter === 'deposit' ? 'selected' : ''; ?>>Пополнения</option>
                                    <option value="withdrawal" <?php echo $type_filter === 'withdrawal' ? 'selected' : ''; ?>>Выводы</option>
                                    <option value="profit" <?php echo $type_filter === 'profit' ? 'selected' : ''; ?>>Прибыль</option>
                                    <option value="referral" <?php echo $type_filter === 'referral' ? 'selected' : ''; ?>>Рефералы</option>
                                    <option value="bonus" <?php echo $type_filter === 'bonus' ? 'selected' : ''; ?>>Бонусы</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="status">Статус:</label>
                                <select name="status" id="status">
                                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Все статусы</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>В ожидании</option>
                                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Одобрено</option>
                                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Отклонено</option>
                                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Отменено</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="date_from">С даты:</label>
                                <input type="date" name="date_from" id="date_from" value="<?php echo e($date_from); ?>">
                            </div>

                            <div class="filter-group">
                                <label for="date_to">По дату:</label>
                                <input type="date" name="date_to" id="date_to" value="<?php echo e($date_to); ?>">
                            </div>

                            <div class="filter-group">
                                <label for="sort">Сортировка:</label>
                                <select name="sort" id="sort">
                                    <option value="created_at" <?php echo $sort_by === 'created_at' ? 'selected' : ''; ?>>По дате</option>
                                    <option value="amount" <?php echo $sort_by === 'amount' ? 'selected' : ''; ?>>По сумме</option>
                                    <option value="type" <?php echo $sort_by === 'type' ? 'selected' : ''; ?>>По типу</option>
                                    <option value="status" <?php echo $sort_by === 'status' ? 'selected' : ''; ?>>По статусу</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="order">Порядок:</label>
                                <select name="order" id="order">
                                    <option value="DESC" <?php echo $sort_order === 'DESC' ? 'selected' : ''; ?>>По убыванию</option>
                                    <option value="ASC" <?php echo $sort_order === 'ASC' ? 'selected' : ''; ?>>По возрастанию</option>
                                </select>
                            </div>

                            <div class="filter-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i>
                                    Применить
                                </button>
                                <a href="transactions.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    Сбросить
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Transactions List -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>История транзакций</h3>
                    <div class="card-actions">
                        <span class="results-count">
                            Показано <?php echo count($transactions); ?> из <?php echo $total_transactions; ?>
                        </span>
                    </div>
                </div>
                <div class="card-content">
                    <?php if (empty($transactions)): ?>
                        <div class="empty-state">
                            <i class="fas fa-receipt"></i>
                            <h3>Транзакций не найдено</h3>
                            <p>У вас пока нет транзакций или они не соответствуют выбранным фильтрам</p>
                            <div class="empty-actions">
                                <a href="deposit.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    Пополнить баланс
                                </a>
                                <a href="invest.php" class="btn btn-outline">
                                    <i class="fas fa-chart-line"></i>
                                    Создать инвестицию
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="transactions-table-container">
                            <table class="transactions-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Тип</th>
                                        <th>Сумма</th>
                                        <th>Статус</th>
                                        <th>Описание</th>
                                        <th>Дата</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transactions as $transaction): ?>
                                        <tr class="transaction-row" data-type="<?php echo $transaction['type']; ?>" data-status="<?php echo $transaction['status']; ?>">
                                            <td>
                                                <div class="transaction-id">
                                                    #<?php echo str_pad($transaction['id'], 6, '0', STR_PAD_LEFT); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="transaction-type">
                                                    <span class="type-icon type-<?php echo $transaction['type']; ?>">
                                                        <?php
                                                        switch($transaction['type']) {
                                                            case 'deposit':
                                                                echo '<i class="fas fa-arrow-down"></i>';
                                                                break;
                                                            case 'withdrawal':
                                                                echo '<i class="fas fa-arrow-up"></i>';
                                                                break;
                                                            case 'profit':
                                                                echo '<i class="fas fa-coins"></i>';
                                                                break;
                                                            case 'referral':
                                                                echo '<i class="fas fa-users"></i>';
                                                                break;
                                                            case 'bonus':
                                                                echo '<i class="fas fa-gift"></i>';
                                                                break;
                                                            default:
                                                                echo '<i class="fas fa-exchange-alt"></i>';
                                                        }
                                                        ?>
                                                    </span>
                                                    <span class="type-text">
                                                        <?php
                                                        switch($transaction['type']) {
                                                            case 'deposit': echo 'Пополнение'; break;
                                                            case 'withdrawal': echo 'Вывод'; break;
                                                            case 'profit': echo 'Прибыль'; break;
                                                            case 'referral': echo 'Реферал'; break;
                                                            case 'bonus': echo 'Бонус'; break;
                                                            default: echo ucfirst($transaction['type']);
                                                        }
                                                        ?>
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="transaction-amount amount-<?php echo $transaction['type']; ?>">
                                                    <?php if (in_array($transaction['type'], ['deposit', 'profit', 'referral', 'bonus'])): ?>
                                                        +$<?php echo formatAmount($transaction['amount'], 2); ?>
                                                    <?php else: ?>
                                                        -$<?php echo formatAmount($transaction['amount'], 2); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                                    <?php
                                                    switch($transaction['status']) {
                                                        case 'pending': echo 'В ожидании'; break;
                                                        case 'approved': echo 'Одобрено'; break;
                                                        case 'rejected': echo 'Отклонено'; break;
                                                        case 'cancelled': echo 'Отменено'; break;
                                                        default: echo ucfirst($transaction['status']);
                                                    }
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="transaction-description">
                                                    <?php echo e($transaction['description'] ?? 'Нет описания'); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="date-info">
                                                    <div class="date"><?php echo formatDate($transaction['created_at'], 'd.m.Y'); ?></div>
                                                    <div class="time"><?php echo formatDate($transaction['created_at'], 'H:i'); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="actions">
                                                    <button class="btn-icon" onclick="viewTransaction(<?php echo $transaction['id']; ?>)" title="Подробности">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($transaction['status'] === 'pending' && in_array($transaction['type'], ['withdrawal'])): ?>
                                                        <button class="btn-icon btn-danger" onclick="cancelTransaction(<?php echo $transaction['id']; ?>)" title="Отменить">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination-container">
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                                Предыдущая
                            </a>
                        <?php endif; ?>

                        <div class="pagination-pages">
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            if ($start_page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>" class="pagination-page">1</a>
                                <?php if ($start_page > 2): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                   class="pagination-page <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>" class="pagination-page"><?php echo $total_pages; ?></a>
                            <?php endif; ?>
                        </div>

                        <?php if ($page < $total_pages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-btn">
                                Следующая
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Transaction Details Modal -->
    <div id="transaction-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Детали транзакции</h3>
                <button class="modal-close" onclick="closeModal('transaction-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="transaction-details">
                <!-- Содержимое загружается через AJAX -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <script>
        // Просмотр деталей транзакции
        function viewTransaction(transactionId) {
            fetch(`api/get-transaction-details.php?id=${transactionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('transaction-details').innerHTML = data.html;
                        openModal('transaction-modal');
                    } else {
                        showNotification(data.message || 'Ошибка загрузки данных', 'error');
                    }
                })
                .catch(error => {
                    showNotification('Ошибка соединения', 'error');
                });
        }

        // Отмена транзакции
        function cancelTransaction(transactionId) {
            if (!confirm('Вы уверены, что хотите отменить эту транзакцию?')) {
                return;
            }

            fetch('api/cancel-transaction.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    csrf_token: '<?php echo generateCSRFToken(); ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Транзакция отменена', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification(data.message || 'Ошибка отмены транзакции', 'error');
                }
            })
            .catch(error => {
                showNotification('Ошибка соединения', 'error');
            });
        }

        // Автоматическое применение фильтров при изменении
        document.querySelectorAll('.filters-form select, .filters-form input[type="date"]').forEach(element => {
            element.addEventListener('change', function() {
                this.form.submit();
            });
        });

        // Экспорт транзакций
        function exportTransactions() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'csv');
            window.open(`api/export-transactions.php?${params.toString()}`, '_blank');
        }
    </script>
</body>
</html>
