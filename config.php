<?php
/**
 * AstroGenix Configuration File
 * Основные настройки платформы стейкинга USDT
 */

// Предотвращение прямого доступа
if (!defined('ASTROGENIX_INIT')) {
    die('Direct access not allowed');
}

// Настройки базы данных
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'admin');
define('DB_PASS', 'admin');
define('DB_CHARSET', 'utf8mb4');

// Основные настройки сайта
define('SITE_URL', 'http://genix/');
define('SITE_PATH', __DIR__);
define('ADMIN_PATH', SITE_PATH . '/admin');
define('UPLOADS_PATH', SITE_PATH . '/uploads');
define('UPLOADS_URL', SITE_URL . '/uploads');

// Настройки безопасности
define('SECURITY_SALT', 'astrogenix_2024_secure_salt_key');
define('SESSION_LIFETIME', 3600 * 24 * 7); // 7 дней
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 минут

// Настройки файлов
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_DOC_TYPES', ['pdf', 'doc', 'docx', 'txt']);

// Настройки email
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'AstroGenix');

// Часовой пояс
date_default_timezone_set('UTC');

// Настройки отображения ошибок (для разработки)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Автозагрузка классов
spl_autoload_register(function ($class) {
    $file = SITE_PATH . '/includes/classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Подключение к базе данных
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Инициализация сессии
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Загрузка функций
require_once SITE_PATH . '/includes/functions.php';

// Проверка режима технических работ
if (getSetting('maintenance_mode') && !isAdmin()) {
    include SITE_PATH . '/maintenance.php';
    exit;
}



/**
 * Получение IP адреса пользователя
 */
function getUserIP() {
    $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * Определение страны по IP (заглушка)
 */
function getCountryByIP($ip) {
    // В реальном проекте здесь должен быть API для определения страны
    // Например, MaxMind GeoIP или аналогичный сервис
    return 'US';
}

/**
 * Проверка, является ли пользователь администратором
 */
function isAdmin() {
    return isset($_SESSION['admin_id']) && $_SESSION['admin_id'] > 0;
}

/**
 * Получение настройки из базы данных
 */
function getSetting($key, $default = null) {
    static $settings = [];
    
    if (empty($settings)) {
        try {
            $stmt = $GLOBALS['pdo']->query("SELECT setting_key, setting_value FROM site_settings");
            while ($row = $stmt->fetch()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (PDOException $e) {
            return $default;
        }
    }
    
    return $settings[$key] ?? $default;
}



/**
 * Экранирование HTML
 */
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Форматирование суммы
 */
function formatAmount($amount, $decimals = 8) {
    return number_format($amount, $decimals, '.', '');
}

/**
 * Генерация CSRF токена
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Проверка CSRF токена
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
