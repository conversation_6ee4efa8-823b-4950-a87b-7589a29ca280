<?php
/**
 * AstroGenix - Change Language API
 * API для смены языка интерфейса
 */

define('ASTROGENIX_INIT', true);
require_once '../config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $language = $input['language'] ?? '';
    
    // Проверка поддерживаемых языков
    $supported_languages = ['en', 'ru'];
    if (!in_array($language, $supported_languages)) {
        throw new Exception('Unsupported language');
    }
    
    // Сохранение языка в сессии
    $_SESSION['language'] = $language;
    
    // Если пользователь авторизован, обновляем язык в базе данных
    if (isset($_SESSION['user_id'])) {
        $stmt = $pdo->prepare("UPDATE users SET language = ? WHERE id = ?");
        $stmt->execute([$language, $_SESSION['user_id']]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Language changed successfully',
        'language' => $language
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
