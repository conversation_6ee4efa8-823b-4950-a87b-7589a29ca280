/**
 * AstroGenix Authentication Styles
 * Стили для страниц авторизации и регистрации
 */

/* Auth Page Layout */
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.auth-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    z-index: 1;
}

.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(240, 185, 11, 0.05) 0%, transparent 70%);
    z-index: -1;
}

.auth-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(240, 185, 11, 0.2), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 212, 170, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.1), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: float 20s infinite linear;
}

/* Auth Logo */
.auth-logo {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-logo a {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.auth-logo img {
    height: 40px;
}

.auth-logo span {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Auth Card */
.auth-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.auth-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    margin-bottom: 0;
}

/* Auth Form */
.auth-form {
    margin-bottom: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

@media (max-width: 480px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(240, 185, 11, 0.1);
}

.form-group input::placeholder {
    color: var(--text-muted);
}

.form-group input.error {
    border-color: var(--danger-color);
}

.field-error {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.field-error::before {
    content: '⚠';
}

/* Password Input */
.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--text-primary);
}

/* Password Strength */
.password-strength {
    margin-top: var(--spacing-sm);
    height: 4px;
    background: var(--bg-input);
    border-radius: var(--radius-sm);
    overflow: hidden;
    position: relative;
}

.password-strength::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    transition: var(--transition-base);
    border-radius: var(--radius-sm);
}

.password-strength.weak::after {
    width: 33%;
    background: var(--danger-color);
}

.password-strength.medium::after {
    width: 66%;
    background: var(--warning-color);
}

.password-strength.strong::after {
    width: 100%;
    background: var(--success-color);
}

/* Checkbox */
.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    flex-shrink: 0;
    transition: var(--transition-base);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-inverse);
    font-size: var(--font-size-sm);
    font-weight: bold;
}

/* Alerts */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.alert i {
    flex-shrink: 0;
    margin-top: 2px;
}

.alert-error {
    background: rgba(246, 70, 93, 0.1);
    border: 1px solid rgba(246, 70, 93, 0.2);
    color: var(--danger-color);
}

.alert-success {
    background: rgba(0, 212, 170, 0.1);
    border: 1px solid rgba(0, 212, 170, 0.2);
    color: var(--success-color);
}

.alert-info {
    background: rgba(24, 144, 255, 0.1);
    border: 1px solid rgba(24, 144, 255, 0.2);
    color: var(--info-color);
}

.alert-warning {
    background: rgba(252, 213, 53, 0.1);
    border: 1px solid rgba(252, 213, 53, 0.2);
    color: var(--warning-color);
}

/* Loading Redirect */
.loading-redirect {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--success-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.auth-footer a {
    color: var(--primary-color);
    font-weight: 500;
}

/* Registration Benefits */
.registration-benefits {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: rgba(240, 185, 11, 0.05);
    border: 1px solid rgba(240, 185, 11, 0.1);
    border-radius: var(--radius-lg);
}

.registration-benefits h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.registration-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.registration-benefits li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.registration-benefits li:last-child {
    margin-bottom: 0;
}

.registration-benefits li i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

/* Social Login */
.social-login {
    margin: var(--spacing-xl) 0;
}

.social-divider {
    position: relative;
    text-align: center;
    margin: var(--spacing-lg) 0;
}

.social-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.social-divider span {
    background: var(--bg-card);
    padding: 0 var(--spacing-md);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.social-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.btn-social {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-base);
}

.btn-social:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
}

.btn-social i {
    font-size: var(--font-size-lg);
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-card {
        padding: var(--spacing-lg);
    }
    
    .auth-header h1 {
        font-size: var(--font-size-2xl);
    }
    
    .social-buttons {
        grid-template-columns: 1fr;
    }
}

/* Animations */
.auth-card {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Validation States */
.form-group.success input {
    border-color: var(--success-color);
}

.form-group.success::after {
    content: '✓';
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--success-color);
    font-weight: bold;
}

/* Two Factor Authentication */
.two-factor-input {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    margin: var(--spacing-lg) 0;
}

.two-factor-input input {
    width: 50px;
    height: 50px;
    text-align: center;
    font-size: var(--font-size-lg);
    font-weight: bold;
    border-radius: var(--radius-md);
}

/* Remember Me */
.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: var(--spacing-lg) 0;
    font-size: var(--font-size-sm);
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Login Stats */
.login-stats {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: rgba(0, 212, 170, 0.05);
    border: 1px solid rgba(0, 212, 170, 0.1);
    border-radius: var(--radius-lg);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
}

@media (min-width: 480px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-md);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-secondary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-lg);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* Security Notice */
.security-notice {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: rgba(24, 144, 255, 0.05);
    border: 1px solid rgba(24, 144, 255, 0.1);
    border-radius: var(--radius-lg);
}

.notice-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    color: var(--info-color);
}

.security-notice ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.security-notice li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.security-notice li:last-child {
    margin-bottom: 0;
}

.security-notice li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--info-color);
    font-weight: bold;
}

/* Demo Credentials */
.demo-credentials {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(252, 213, 53, 0.1);
    border: 1px solid rgba(252, 213, 53, 0.2);
    border-radius: var(--radius-md);
    text-align: center;
}

.demo-credentials h4 {
    color: var(--warning-color);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.demo-credentials p {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin: 0;
}

.demo-credentials code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: 'Courier New', monospace;
    color: var(--text-primary);
}

/* Login Attempts Warning */
.login-attempts {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(246, 70, 93, 0.1);
    border: 1px solid rgba(246, 70, 93, 0.2);
    border-radius: var(--radius-md);
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    text-align: center;
}

/* Maintenance Mode */
.maintenance-notice {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--warning-color);
    color: var(--text-inverse);
    padding: var(--spacing-sm);
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: 500;
    z-index: var(--z-fixed);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Captcha */
.captcha-container {
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.captcha-image {
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    font-family: monospace;
    font-size: var(--font-size-lg);
    letter-spacing: 4px;
    user-select: none;
}

.captcha-refresh {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: var(--font-size-sm);
    text-decoration: underline;
}

/* Mobile Optimizations */
@media (max-width: 480px) {
    .login-stats {
        padding: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        justify-content: center;
        text-align: center;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .security-notice {
        padding: var(--spacing-md);
    }

    .security-notice li {
        padding-left: var(--spacing-md);
    }
}
