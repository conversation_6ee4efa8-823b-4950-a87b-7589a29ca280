<?php
/**
 * AstroGenix - Deposit Page
 * Страница пополнения баланса
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

$error = '';
$success = '';

// Обработка формы пополнения
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $amount = floatval($_POST['amount'] ?? 0);
        $payment_method = $_POST['payment_method'] ?? '';
        $wallet_address = trim($_POST['wallet_address'] ?? '');
        
        // Валидация
        if ($amount < 10) {
            $error = 'Минимальная сумма пополнения: $10';
        } elseif ($amount > 50000) {
            $error = 'Максимальная сумма пополнения: $50,000';
        } elseif (empty($payment_method)) {
            $error = 'Выберите способ оплаты';
        } elseif (empty($wallet_address)) {
            $error = 'Укажите адрес кошелька для отправки';
        } else {
            try {
                // Создание транзакции пополнения
                $stmt = $pdo->prepare("
                    INSERT INTO transactions (user_id, type, amount, status, description, payment_method, wallet_address, created_at) 
                    VALUES (?, 'deposit', ?, 'pending', ?, ?, ?, NOW())
                ");
                
                $description = "Пополнение баланса через {$payment_method}";
                
                if ($stmt->execute([$user['id'], $amount, $description, $payment_method, $wallet_address])) {
                    $transaction_id = $pdo->lastInsertId();
                    
                    // Логирование
                    logActivity($user['id'], 'deposit_request', "Запрос на пополнение #{$transaction_id} на сумму ${amount}");
                    
                    $success = "Запрос на пополнение создан! ID транзакции: #{$transaction_id}. Переведите средства на указанный адрес и ожидайте подтверждения.";
                } else {
                    $error = 'Ошибка создания запроса на пополнение';
                }
            } catch (PDOException $e) {
                $error = 'Ошибка базы данных';
                error_log("Deposit error: " . $e->getMessage());
            }
        }
    }
}

// Получение последних транзакций пополнения
try {
    $recent_deposits_stmt = $pdo->prepare("
        SELECT * FROM transactions 
        WHERE user_id = ? AND type = 'deposit' 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $recent_deposits_stmt->execute([$user['id']]);
    $recent_deposits = $recent_deposits_stmt->fetchAll();
} catch (PDOException $e) {
    $recent_deposits = [];
}

// Получение адресов кошельков для пополнения
$wallet_addresses = [
    'USDT_TRC20' => getSetting('usdt_trc20_address', 'TXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx'),
    'USDT_ERC20' => getSetting('usdt_erc20_address', '0xXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'),
    'BTC' => getSetting('btc_address', '**********************************'),
    'ETH' => getSetting('eth_address', '0xXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'),
];

$page_title = __('deposit') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('deposit_funds'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
                
                <div class="header-actions">
                    <a href="transactions.php" class="btn btn-outline btn-sm">
                        <i class="fas fa-history"></i>
                        История
                    </a>
                </div>
            </div>
        </header>
        
        <!-- Deposit Content -->
        <div class="dashboard-content">
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo e($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo e($success); ?>
                </div>
            <?php endif; ?>
            
            <!-- Deposit Form -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Пополнение баланса</h3>
                    <div class="card-subtitle">
                        Минимум: $10 | Максимум: $50,000
                    </div>
                </div>
                <div class="card-content">
                    <form method="POST" class="deposit-form" id="deposit-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="amount">Сумма пополнения (USD)</label>
                                <div class="input-group">
                                    <span class="input-prefix">$</span>
                                    <input type="number" 
                                           id="amount" 
                                           name="amount" 
                                           min="10" 
                                           max="50000" 
                                           step="0.01" 
                                           placeholder="100.00"
                                           required>
                                </div>
                                <div class="form-help">
                                    Введите сумму от $10 до $50,000
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="payment_method">Способ оплаты</label>
                                <select id="payment_method" name="payment_method" required>
                                    <option value="">Выберите способ оплаты</option>
                                    <option value="USDT_TRC20">USDT (TRC-20)</option>
                                    <option value="USDT_ERC20">USDT (ERC-20)</option>
                                    <option value="BTC">Bitcoin (BTC)</option>
                                    <option value="ETH">Ethereum (ETH)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="wallet_address">Ваш адрес кошелька</label>
                                <input type="text" 
                                       id="wallet_address" 
                                       name="wallet_address" 
                                       placeholder="Введите адрес вашего кошелька"
                                       required>
                                <div class="form-help">
                                    Адрес кошелька, с которого вы будете отправлять средства
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Address Display -->
                        <div id="payment-address-section" class="payment-address-section" style="display: none;">
                            <h4>Адрес для перевода</h4>
                            <div class="address-display">
                                <div class="address-text" id="payment-address"></div>
                                <button type="button" class="btn-copy" onclick="copyAddress()">
                                    <i class="fas fa-copy"></i>
                                    Копировать
                                </button>
                            </div>
                            <div class="payment-instructions">
                                <h5>Инструкции по оплате:</h5>
                                <ol>
                                    <li>Скопируйте адрес выше</li>
                                    <li>Отправьте точную сумму на этот адрес</li>
                                    <li>Нажмите "Подтвердить пополнение"</li>
                                    <li>Ожидайте подтверждения (обычно 10-30 минут)</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus"></i>
                                Подтвердить пополнение
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                Назад
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Deposits -->
            <?php if (!empty($recent_deposits)): ?>
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Последние пополнения</h3>
                    </div>
                    <div class="card-content">
                        <div class="recent-transactions">
                            <?php foreach ($recent_deposits as $deposit): ?>
                                <div class="transaction-item">
                                    <div class="transaction-icon">
                                        <i class="fas fa-arrow-down"></i>
                                    </div>
                                    <div class="transaction-info">
                                        <div class="transaction-title">
                                            Пополнение #<?php echo str_pad($deposit['id'], 6, '0', STR_PAD_LEFT); ?>
                                        </div>
                                        <div class="transaction-details">
                                            <?php echo e($deposit['payment_method']); ?> •
                                            <?php echo formatDate($deposit['created_at'], 'd.m.Y H:i'); ?>
                                        </div>
                                    </div>
                                    <div class="transaction-amount">
                                        +$<?php echo formatAmount($deposit['amount'], 2); ?>
                                    </div>
                                    <div class="transaction-status">
                                        <span class="status-badge status-<?php echo $deposit['status']; ?>">
                                            <?php
                                            switch($deposit['status']) {
                                                case 'pending': echo 'В ожидании'; break;
                                                case 'approved': echo 'Одобрено'; break;
                                                case 'rejected': echo 'Отклонено'; break;
                                                default: echo ucfirst($deposit['status']);
                                            }
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="card-footer">
                            <a href="transactions.php?type=deposit" class="btn btn-outline">
                                Посмотреть все пополнения
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Deposit Info -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Информация о пополнении</h3>
                </div>
                <div class="card-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="info-content">
                                <h4>Время обработки</h4>
                                <p>Обычно 10-30 минут после подтверждения в блокчейне</p>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="info-content">
                                <h4>Безопасность</h4>
                                <p>Все транзакции проверяются автоматически и вручную</p>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="info-content">
                                <h4>Поддержка</h4>
                                <p>Если возникли проблемы, обратитесь в службу поддержки</p>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="info-content">
                                <h4>Комиссия</h4>
                                <p>Комиссия платформы: 0%. Комиссия сети оплачивается отдельно</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <script>
        // Адреса кошельков
        const walletAddresses = <?php echo json_encode($wallet_addresses); ?>;

        // Обработка выбора способа оплаты
        document.getElementById('payment_method').addEventListener('change', function() {
            const method = this.value;
            const addressSection = document.getElementById('payment-address-section');
            const addressElement = document.getElementById('payment-address');

            if (method && walletAddresses[method]) {
                addressElement.textContent = walletAddresses[method];
                addressSection.style.display = 'block';
            } else {
                addressSection.style.display = 'none';
            }
        });

        // Копирование адреса
        function copyAddress() {
            const addressText = document.getElementById('payment-address').textContent;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(addressText).then(() => {
                    showNotification('Адрес скопирован в буфер обмена', 'success');
                });
            } else {
                // Fallback для старых браузеров
                const textArea = document.createElement('textarea');
                textArea.value = addressText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('Адрес скопирован в буфер обмена', 'success');
            }
        }

        // Валидация формы
        document.getElementById('deposit-form').addEventListener('submit', function(e) {
            const amount = parseFloat(document.getElementById('amount').value);
            const paymentMethod = document.getElementById('payment_method').value;
            const walletAddress = document.getElementById('wallet_address').value.trim();

            if (amount < 10) {
                e.preventDefault();
                showNotification('Минимальная сумма пополнения: $10', 'error');
                return;
            }

            if (amount > 50000) {
                e.preventDefault();
                showNotification('Максимальная сумма пополнения: $50,000', 'error');
                return;
            }

            if (!paymentMethod) {
                e.preventDefault();
                showNotification('Выберите способ оплаты', 'error');
                return;
            }

            if (!walletAddress) {
                e.preventDefault();
                showNotification('Укажите адрес вашего кошелька', 'error');
                return;
            }

            // Подтверждение
            if (!confirm(`Вы уверены, что хотите создать запрос на пополнение на сумму $${amount.toFixed(2)}?`)) {
                e.preventDefault();
            }
        });

        // Автоматическое форматирование суммы
        document.getElementById('amount').addEventListener('input', function() {
            let value = this.value;
            if (value && !isNaN(value)) {
                this.value = parseFloat(value).toFixed(2);
            }
        });
    </script>
</body>
</html>
