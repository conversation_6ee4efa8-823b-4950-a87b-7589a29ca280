<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance - AstroGenix</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0b0e11 0%, #1e2329 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .maintenance-container {
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f0b90b 0%, #d9a441 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .maintenance-icon {
            font-size: 4rem;
            color: #f0b90b;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #f0b90b 0%, #d9a441 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #b7bdc6;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .description {
            color: #848e9c;
            margin-bottom: 3rem;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature {
            padding: 1.5rem;
            background: rgba(30, 35, 41, 0.5);
            border: 1px solid rgba(240, 185, 11, 0.2);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #f0b90b;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .feature-desc {
            font-size: 0.9rem;
            color: #848e9c;
        }
        
        .contact-info {
            background: rgba(30, 35, 41, 0.3);
            border: 1px solid rgba(240, 185, 11, 0.1);
            border-radius: 12px;
            padding: 2rem;
            margin-top: 2rem;
        }
        
        .contact-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #f0b90b;
        }
        
        .contact-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .contact-link {
            color: #b7bdc6;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: color 0.3s ease;
        }
        
        .contact-link:hover {
            color: #f0b90b;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin: 2rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #f0b90b, #d9a441);
            width: 0%;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 0%; }
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(0, 212, 170, 0.1);
            color: #00d4aa;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-bottom: 2rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #00d4aa;
            border-radius: 50%;
            animation: blink 1.5s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .contact-links {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="logo">AG</div>
        
        <div class="maintenance-icon">🔧</div>
        
        <div class="status-indicator">
            <div class="status-dot"></div>
            Система обновляется
        </div>
        
        <h1>Технические работы</h1>
        
        <p class="subtitle">
            Мы временно приостановили работу платформы для внедрения новых функций и улучшений
        </p>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <p class="description">
            Наша команда работает над улучшением платформы, чтобы предоставить вам еще более качественный сервис. 
            Ожидаемое время завершения работ: <strong>30-60 минут</strong>.
        </p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Повышение производительности</div>
                <div class="feature-desc">Оптимизация скорости работы платформы</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔒</div>
                <div class="feature-title">Улучшение безопасности</div>
                <div class="feature-desc">Внедрение дополнительных мер защиты</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">✨</div>
                <div class="feature-title">Новые функции</div>
                <div class="feature-desc">Добавление новых возможностей</div>
            </div>
        </div>
        
        <div class="contact-info">
            <div class="contact-title">Остались вопросы?</div>
            <div class="contact-links">
                <a href="mailto:<EMAIL>" class="contact-link">
                    📧 <EMAIL>
                </a>
                <a href="https://t.me/astrogenix" class="contact-link">
                    💬 Telegram
                </a>
                <a href="https://twitter.com/astrogenix" class="contact-link">
                    🐦 Twitter
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Автоматическое обновление страницы каждые 5 минут
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 минут
        
        // Показать примерное время завершения
        const now = new Date();
        const endTime = new Date(now.getTime() + 45 * 60000); // +45 минут
        
        function updateCountdown() {
            const now = new Date();
            const timeLeft = endTime - now;
            
            if (timeLeft > 0) {
                const minutes = Math.floor(timeLeft / 60000);
                const seconds = Math.floor((timeLeft % 60000) / 1000);
                
                document.title = `Maintenance (${minutes}:${seconds.toString().padStart(2, '0')}) - AstroGenix`;
            } else {
                document.title = 'Maintenance - AstroGenix';
                location.reload();
            }
        }
        
        // Обновляем каждую секунду
        setInterval(updateCountdown, 1000);
        updateCountdown();
    </script>
</body>
</html>
