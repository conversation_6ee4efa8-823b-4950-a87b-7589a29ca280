<?php
/**
 * AstroGenix - Tasks Page
 * Страница задач и достижений
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

$error = '';
$success = '';

// Обработка выполнения задачи
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['complete_task'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $task_id = intval($_POST['task_id'] ?? 0);
        
        try {
            // Проверяем, что задача существует и не выполнена
            $task_stmt = $pdo->prepare("
                SELECT t.*, ut.id as user_task_id 
                FROM tasks t 
                LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
                WHERE t.id = ? AND t.status = 'active'
            ");
            $task_stmt->execute([$user['id'], $task_id]);
            $task = $task_stmt->fetch();
            
            if (!$task) {
                $error = 'Задача не найдена';
            } elseif ($task['user_task_id']) {
                $error = 'Задача уже выполнена';
            } else {
                // Проверяем условия выполнения задачи
                $can_complete = false;
                $completion_data = [];
                
                switch ($task['type']) {
                    case 'registration':
                        $can_complete = true; // Регистрация уже выполнена
                        break;
                        
                    case 'first_deposit':
                        $deposit_stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM transactions 
                            WHERE user_id = ? AND type = 'deposit' AND status = 'approved'
                        ");
                        $deposit_stmt->execute([$user['id']]);
                        $can_complete = $deposit_stmt->fetchColumn() > 0;
                        break;
                        
                    case 'first_investment':
                        $investment_stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM user_investments 
                            WHERE user_id = ? AND status IN ('active', 'completed')
                        ");
                        $investment_stmt->execute([$user['id']]);
                        $can_complete = $investment_stmt->fetchColumn() > 0;
                        break;
                        
                    case 'total_investment':
                        $total_stmt = $pdo->prepare("
                            SELECT COALESCE(SUM(amount), 0) FROM user_investments 
                            WHERE user_id = ? AND status IN ('active', 'completed')
                        ");
                        $total_stmt->execute([$user['id']]);
                        $total_invested = $total_stmt->fetchColumn();
                        $can_complete = $total_invested >= $task['target_value'];
                        $completion_data['total_invested'] = $total_invested;
                        break;
                        
                    case 'referral_count':
                        $referral_stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM users WHERE referrer_id = ?
                        ");
                        $referral_stmt->execute([$user['id']]);
                        $referral_count = $referral_stmt->fetchColumn();
                        $can_complete = $referral_count >= $task['target_value'];
                        $completion_data['referral_count'] = $referral_count;
                        break;
                        
                    case 'daily_login':
                        // Проверяем ежедневный вход (упрощенная логика)
                        $can_complete = true;
                        break;
                }
                
                if (!$can_complete) {
                    $error = 'Условия задачи еще не выполнены';
                } else {
                    $pdo->beginTransaction();
                    
                    // Отмечаем задачу как выполненную
                    $complete_stmt = $pdo->prepare("
                        INSERT INTO user_tasks (user_id, task_id, completed_at, completion_data) 
                        VALUES (?, ?, NOW(), ?)
                    ");
                    $complete_stmt->execute([
                        $user['id'], 
                        $task_id, 
                        json_encode($completion_data)
                    ]);
                    
                    // Начисляем награду
                    if ($task['reward_amount'] > 0) {
                        $update_balance_stmt = $pdo->prepare("
                            UPDATE users SET balance = balance + ? WHERE id = ?
                        ");
                        $update_balance_stmt->execute([$task['reward_amount'], $user['id']]);
                        
                        // Создаем транзакцию
                        $transaction_stmt = $pdo->prepare("
                            INSERT INTO transactions (user_id, type, amount, status, description, created_at) 
                            VALUES (?, 'bonus', ?, 'approved', ?, NOW())
                        ");
                        $transaction_stmt->execute([
                            $user['id'], 
                            $task['reward_amount'], 
                            "Награда за выполнение задачи: {$task['title']}"
                        ]);
                    }
                    
                    // Логирование
                    logActivity($user['id'], 'task_completed', "Выполнена задача: {$task['title']}");
                    
                    $pdo->commit();
                    
                    $success = "Задача выполнена! Награда \${$task['reward_amount']} зачислена на ваш баланс.";
                    
                    // Обновляем баланс пользователя
                    $user['balance'] += $task['reward_amount'];
                }
            }
        } catch (PDOException $e) {
            $pdo->rollBack();
            $error = 'Ошибка базы данных';
            error_log("Task completion error: " . $e->getMessage());
        }
    }
}

// Получение задач
try {
    // Все доступные задачи с информацией о выполнении
    $tasks_stmt = $pdo->prepare("
        SELECT t.*, 
               ut.id as user_task_id,
               ut.completed_at,
               ut.completion_data,
               CASE 
                   WHEN ut.id IS NOT NULL THEN 'completed'
                   WHEN t.status = 'active' THEN 'available'
                   ELSE 'unavailable'
               END as user_status
        FROM tasks t 
        LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
        WHERE t.status IN ('active', 'inactive')
        ORDER BY t.sort_order, t.created_at
    ");
    $tasks_stmt->execute([$user['id']]);
    $tasks = $tasks_stmt->fetchAll();
    
    // Статистика выполнения задач
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN ut.id IS NOT NULL THEN 1 END) as completed_count,
            COUNT(t.id) as total_count,
            COALESCE(SUM(CASE WHEN ut.id IS NOT NULL THEN t.reward_amount ELSE 0 END), 0) as total_earned
        FROM tasks t 
        LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
        WHERE t.status = 'active'
    ");
    $stats_stmt->execute([$user['id']]);
    $task_stats = $stats_stmt->fetch();
    
} catch (PDOException $e) {
    $tasks = [];
    $task_stats = [
        'completed_count' => 0,
        'total_count' => 0,
        'total_earned' => 0
    ];
}

$page_title = __('tasks') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('tasks_achievements'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
            </div>
        </header>
        
        <!-- Tasks Content -->
        <div class="dashboard-content">
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo e($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo e($success); ?>
                </div>
            <?php endif; ?>
            
            <!-- Task Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $task_stats['completed_count']; ?> / <?php echo $task_stats['total_count']; ?></div>
                        <div class="stat-label">Выполнено задач</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">
                            <?php echo $task_stats['total_count'] > 0 ? round(($task_stats['completed_count'] / $task_stats['total_count']) * 100) : 0; ?>%
                        </div>
                        <div class="stat-label">Прогресс</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($task_stats['total_earned'], 2); ?></div>
                        <div class="stat-label">Заработано</div>
                    </div>
                </div>
            </div>

            <!-- Tasks List -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Доступные задачи</h3>
                    <div class="card-subtitle">
                        Выполняйте задачи и получайте награды
                    </div>
                </div>
                <div class="card-content">
                    <?php if (empty($tasks)): ?>
                        <div class="empty-state">
                            <i class="fas fa-tasks"></i>
                            <h3>Задач не найдено</h3>
                            <p>В данный момент нет доступных задач</p>
                        </div>
                    <?php else: ?>
                        <div class="tasks-grid">
                            <?php foreach ($tasks as $task): ?>
                                <div class="task-card task-<?php echo $task['user_status']; ?>" data-task-id="<?php echo $task['id']; ?>">
                                    <div class="task-header">
                                        <div class="task-icon">
                                            <?php
                                            switch($task['type']) {
                                                case 'registration':
                                                    echo '<i class="fas fa-user-plus"></i>';
                                                    break;
                                                case 'first_deposit':
                                                    echo '<i class="fas fa-credit-card"></i>';
                                                    break;
                                                case 'first_investment':
                                                    echo '<i class="fas fa-chart-line"></i>';
                                                    break;
                                                case 'total_investment':
                                                    echo '<i class="fas fa-dollar-sign"></i>';
                                                    break;
                                                case 'referral_count':
                                                    echo '<i class="fas fa-users"></i>';
                                                    break;
                                                case 'daily_login':
                                                    echo '<i class="fas fa-calendar-day"></i>';
                                                    break;
                                                default:
                                                    echo '<i class="fas fa-star"></i>';
                                            }
                                            ?>
                                        </div>
                                        <div class="task-status">
                                            <?php if ($task['user_status'] === 'completed'): ?>
                                                <i class="fas fa-check-circle text-success"></i>
                                            <?php elseif ($task['user_status'] === 'available'): ?>
                                                <i class="fas fa-clock text-warning"></i>
                                            <?php else: ?>
                                                <i class="fas fa-lock text-muted"></i>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="task-content">
                                        <h4 class="task-title"><?php echo e($task['title']); ?></h4>
                                        <p class="task-description"><?php echo e($task['description']); ?></p>

                                        <?php if ($task['target_value'] > 0): ?>
                                            <div class="task-progress">
                                                <?php
                                                $current_value = 0;
                                                $progress_percent = 0;

                                                // Получаем текущий прогресс
                                                switch ($task['type']) {
                                                    case 'total_investment':
                                                        $progress_stmt = $pdo->prepare("
                                                            SELECT COALESCE(SUM(amount), 0) FROM user_investments
                                                            WHERE user_id = ? AND status IN ('active', 'completed')
                                                        ");
                                                        $progress_stmt->execute([$user['id']]);
                                                        $current_value = $progress_stmt->fetchColumn();
                                                        break;

                                                    case 'referral_count':
                                                        $progress_stmt = $pdo->prepare("
                                                            SELECT COUNT(*) FROM users WHERE referrer_id = ?
                                                        ");
                                                        $progress_stmt->execute([$user['id']]);
                                                        $current_value = $progress_stmt->fetchColumn();
                                                        break;
                                                }

                                                if ($task['target_value'] > 0) {
                                                    $progress_percent = min(100, ($current_value / $task['target_value']) * 100);
                                                }
                                                ?>

                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: <?php echo $progress_percent; ?>%"></div>
                                                </div>
                                                <div class="progress-text">
                                                    <?php if ($task['type'] === 'total_investment'): ?>
                                                        $<?php echo formatAmount($current_value, 2); ?> / $<?php echo formatAmount($task['target_value'], 2); ?>
                                                    <?php else: ?>
                                                        <?php echo $current_value; ?> / <?php echo $task['target_value']; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <div class="task-reward">
                                            <span class="reward-label">Награда:</span>
                                            <span class="reward-amount">$<?php echo formatAmount($task['reward_amount'], 2); ?></span>
                                        </div>

                                        <?php if ($task['user_status'] === 'completed'): ?>
                                            <div class="task-completed">
                                                <i class="fas fa-check"></i>
                                                Выполнено <?php echo formatDate($task['completed_at'], 'd.m.Y H:i'); ?>
                                            </div>
                                        <?php elseif ($task['user_status'] === 'available'): ?>
                                            <form method="POST" class="task-form">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                                <button type="submit" name="complete_task" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-play"></i>
                                                    Выполнить
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <div class="task-unavailable">
                                                <i class="fas fa-lock"></i>
                                                Недоступно
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Task Categories Info -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Типы задач</h3>
                </div>
                <div class="card-content">
                    <div class="task-types-grid">
                        <div class="task-type">
                            <div class="type-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="type-info">
                                <h4>Регистрация</h4>
                                <p>Задачи, связанные с регистрацией и настройкой аккаунта</p>
                            </div>
                        </div>

                        <div class="task-type">
                            <div class="type-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="type-info">
                                <h4>Инвестиции</h4>
                                <p>Задачи по созданию и управлению инвестициями</p>
                            </div>
                        </div>

                        <div class="task-type">
                            <div class="type-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="type-info">
                                <h4>Рефералы</h4>
                                <p>Задачи по привлечению новых пользователей</p>
                            </div>
                        </div>

                        <div class="task-type">
                            <div class="type-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="type-info">
                                <h4>Активность</h4>
                                <p>Ежедневные задачи и достижения</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <script>
        // Подтверждение выполнения задачи
        document.querySelectorAll('.task-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const taskCard = this.closest('.task-card');
                const taskTitle = taskCard.querySelector('.task-title').textContent;

                if (!confirm(`Вы уверены, что хотите выполнить задачу "${taskTitle}"?`)) {
                    e.preventDefault();
                }
            });
        });

        // Анимация прогресс-баров
        document.addEventListener('DOMContentLoaded', function() {
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.transition = 'width 1s ease-in-out';
                    bar.style.width = width;
                }, 100);
            });
        });
    </script>
</body>
</html>
