<?php
/**
 * AstroGenix Functions
 * Основные функции платформы
 */

if (!defined('ASTROGENIX_INIT')) {
    die('Direct access not allowed');
}

/**
 * Функции аутентификации
 */

/**
 * Регистрация нового пользователя
 */
function registerUser($data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Проверка существования пользователя
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
        $stmt->execute([$data['email'], $data['username']]);
        if ($stmt->fetch()) {
            throw new Exception(__('user_already_exists'));
        }
        
        // Генерация реферального кода
        $referral_code = generateReferralCode();
        
        // Создание пользователя
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, phone, country, referrer_id, referral_code, language) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data['username'],
            $data['email'],
            password_hash($data['password'], PASSWORD_DEFAULT),
            $data['first_name'] ?? '',
            $data['last_name'] ?? '',
            $data['phone'] ?? '',
            $data['country'] ?? '',
            $data['referrer_id'] ?? null,
            $referral_code,
            $data['language'] ?? 'en'
        ]);
        
        $user_id = $pdo->lastInsertId();
        
        // Начисление бонуса за регистрацию
        $registration_bonus = getSetting('registration_bonus', 0);
        if ($registration_bonus > 0) {
            addTransaction($user_id, 'bonus', $registration_bonus, 'approved', 'Registration bonus');
            updateUserBalance($user_id, $registration_bonus);
        }
        
        // Обновление статистики реферера
        if (!empty($data['referrer_id'])) {
            $stmt = $pdo->prepare("UPDATE users SET total_referrals = total_referrals + 1 WHERE id = ?");
            $stmt->execute([$data['referrer_id']]);
        }
        
        $pdo->commit();
        return $user_id;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * Авторизация пользователя
 */
function loginUser($email, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['language'] = $user['language'];
        
        // Обновление времени последнего входа
        $stmt = $pdo->prepare("UPDATE users SET last_login = NOW(), login_ip = ? WHERE id = ?");
        $stmt->execute([getUserIP(), $user['id']]);
        
        return true;
    }
    
    return false;
}

/**
 * Получение данных текущего пользователя
 */
function getCurrentUser() {
    global $pdo;
    
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    static $user = null;
    if ($user === null) {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
    }
    
    return $user;
}

/**
 * Проверка авторизации
 */
function requireAuth() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: ' . SITE_URL . '/login.php');
        exit;
    }
}

/**
 * Функции транзакций
 */

/**
 * Добавление транзакции
 */
function addTransaction($user_id, $type, $amount, $status = 'pending', $note = '', $reference_id = null, $reference_type = null) {
    global $pdo;
    
    $fee = 0;
    if ($type === 'withdrawal') {
        $fee_percent = getSetting('withdrawal_fee', 0);
        $fee = $amount * ($fee_percent / 100);
    }
    
    $net_amount = $amount - $fee;
    
    $stmt = $pdo->prepare("
        INSERT INTO transactions (user_id, type, amount, fee, net_amount, status, admin_note, reference_id, reference_type) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    return $stmt->execute([$user_id, $type, $amount, $fee, $net_amount, $status, $note, $reference_id, $reference_type]);
}

/**
 * Обновление баланса пользователя
 */
function updateUserBalance($user_id, $amount, $type = 'add') {
    global $pdo;
    
    $operator = ($type === 'add') ? '+' : '-';
    $stmt = $pdo->prepare("UPDATE users SET balance = balance {$operator} ? WHERE id = ?");
    return $stmt->execute([$amount, $user_id]);
}

/**
 * Функции инвестиций
 */

/**
 * Создание инвестиции
 */
function createInvestment($user_id, $plan_id, $amount) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Получение плана
        $stmt = $pdo->prepare("SELECT * FROM investment_plans WHERE id = ? AND status = 'active'");
        $stmt->execute([$plan_id]);
        $plan = $stmt->fetch();
        
        if (!$plan) {
            throw new Exception(__('invalid_plan'));
        }
        
        // Проверка суммы
        if ($amount < $plan['min_amount'] || $amount > $plan['max_amount']) {
            throw new Exception(__('invalid_amount'));
        }
        
        // Проверка баланса
        $user = getCurrentUser();
        if ($user['balance'] < $amount) {
            throw new Exception(__('insufficient_balance'));
        }
        
        // Создание инвестиции
        $daily_profit = $amount * ($plan['daily_profit'] / 100);
        $end_date = date('Y-m-d H:i:s', strtotime('+' . $plan['duration_days'] . ' days'));
        
        $stmt = $pdo->prepare("
            INSERT INTO user_investments (user_id, plan_id, amount, daily_profit, end_date) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $plan_id, $amount, $daily_profit, $end_date]);
        
        // Списание с баланса
        updateUserBalance($user_id, $amount, 'subtract');
        
        // Обновление статистики пользователя
        $stmt = $pdo->prepare("UPDATE users SET total_invested = total_invested + ? WHERE id = ?");
        $stmt->execute([$amount, $user_id]);
        
        // Добавление транзакции
        addTransaction($user_id, 'investment', $amount, 'approved', 'Investment in ' . $plan['name']);
        
        $pdo->commit();
        return true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * Вспомогательные функции
 */

/**
 * Генерация реферального кода
 */
function generateReferralCode() {
    return strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
}

/**
 * Получение пользователя по реферальному коду
 */
function getUserByReferralCode($code) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE referral_code = ?");
    $stmt->execute([$code]);
    return $stmt->fetch();
}

/**
 * Форматирование даты
 */
function formatDate($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

/**
 * Генерация случайной строки
 */
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Проверка загруженного файла
 */
function validateUploadedFile($file, $allowed_types = null) {
    if ($allowed_types === null) {
        $allowed_types = array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOC_TYPES);
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        throw new Exception(__('invalid_file_type'));
    }
    
    if ($file['size'] > MAX_UPLOAD_SIZE) {
        throw new Exception(__('file_too_large'));
    }
    
    return true;
}

/**
 * Загрузка файла
 */
function uploadFile($file, $directory = 'general') {
    validateUploadedFile($file);
    
    $upload_dir = UPLOADS_PATH . '/' . $directory;
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = uniqid() . '.' . $file_extension;
    $filepath = $upload_dir . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $directory . '/' . $filename;
    }
    
    throw new Exception(__('upload_failed'));
}

/**
 * Отправка email уведомления
 */
function sendEmail($to, $subject, $message, $is_html = true) {
    // Здесь должна быть реализация отправки email
    // Можно использовать PHPMailer или встроенную функцию mail()
    return true;
}

/**
 * Логирование действий
 */
function logAction($action, $details = '', $user_id = null) {
    global $pdo;
    
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    // Здесь можно добавить таблицу для логов и записывать действия
    error_log("[AstroGenix] User: $user_id, Action: $action, Details: $details");
}
