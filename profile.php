<?php
/**
 * AstroGenix - Profile Page
 * Страница профиля пользователя
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

$error = '';
$success = '';

// Обработка обновления профиля
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $first_name = trim($_POST['first_name'] ?? '');
        $last_name = trim($_POST['last_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $country = trim($_POST['country'] ?? '');
        $city = trim($_POST['city'] ?? '');
        $language = $_POST['language'] ?? 'en';
        
        // Валидация
        if (empty($first_name)) {
            $error = 'Укажите имя';
        } elseif (empty($last_name)) {
            $error = 'Укажите фамилию';
        } elseif (empty($email)) {
            $error = 'Укажите email';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Неверный формат email';
        } else {
            // Проверяем, не занят ли email другим пользователем
            $email_check_stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $email_check_stmt->execute([$email, $user['id']]);
            
            if ($email_check_stmt->fetch()) {
                $error = 'Этот email уже используется другим пользователем';
            } else {
                try {
                    $update_stmt = $pdo->prepare("
                        UPDATE users SET 
                            first_name = ?, 
                            last_name = ?, 
                            email = ?, 
                            phone = ?, 
                            country = ?, 
                            city = ?, 
                            language = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    
                    if ($update_stmt->execute([$first_name, $last_name, $email, $phone, $country, $city, $language, $user['id']])) {
                        // Обновляем данные в сессии
                        $_SESSION['user']['first_name'] = $first_name;
                        $_SESSION['user']['last_name'] = $last_name;
                        $_SESSION['user']['email'] = $email;
                        $_SESSION['user']['phone'] = $phone;
                        $_SESSION['user']['country'] = $country;
                        $_SESSION['user']['city'] = $city;
                        $_SESSION['user']['language'] = $language;
                        
                        // Обновляем язык сессии
                        $_SESSION['language'] = $language;
                        
                        logActivity($user['id'], 'profile_updated', 'Профиль обновлен');
                        
                        $success = 'Профиль успешно обновлен!';
                        $user = getCurrentUser(); // Перезагружаем данные пользователя
                    } else {
                        $error = 'Ошибка обновления профиля';
                    }
                } catch (PDOException $e) {
                    $error = 'Ошибка базы данных';
                    error_log("Profile update error: " . $e->getMessage());
                }
            }
        }
    }
}

// Обработка смены пароля
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Валидация
        if (empty($current_password)) {
            $error = 'Введите текущий пароль';
        } elseif (!password_verify($current_password, $user['password'])) {
            $error = 'Неверный текущий пароль';
        } elseif (empty($new_password)) {
            $error = 'Введите новый пароль';
        } elseif (strlen($new_password) < 6) {
            $error = 'Новый пароль должен содержать минимум 6 символов';
        } elseif ($new_password !== $confirm_password) {
            $error = 'Пароли не совпадают';
        } else {
            try {
                $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                $update_stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                
                if ($update_stmt->execute([$password_hash, $user['id']])) {
                    logActivity($user['id'], 'password_changed', 'Пароль изменен');
                    $success = 'Пароль успешно изменен!';
                } else {
                    $error = 'Ошибка изменения пароля';
                }
            } catch (PDOException $e) {
                $error = 'Ошибка базы данных';
                error_log("Password change error: " . $e->getMessage());
            }
        }
    }
}

// Обработка настроек уведомлений
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_notifications'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
        $sms_notifications = isset($_POST['sms_notifications']) ? 1 : 0;
        $push_notifications = isset($_POST['push_notifications']) ? 1 : 0;
        
        try {
            $update_stmt = $pdo->prepare("
                UPDATE users SET 
                    email_notifications = ?, 
                    sms_notifications = ?, 
                    push_notifications = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            if ($update_stmt->execute([$email_notifications, $sms_notifications, $push_notifications, $user['id']])) {
                $success = 'Настройки уведомлений обновлены!';
                $user = getCurrentUser(); // Перезагружаем данные пользователя
            } else {
                $error = 'Ошибка обновления настроек';
            }
        } catch (PDOException $e) {
            $error = 'Ошибка базы данных';
            error_log("Notifications update error: " . $e->getMessage());
        }
    }
}

// Получение статистики пользователя
try {
    $stats_stmt = $pdo->prepare("
        SELECT 
            (SELECT COUNT(*) FROM user_investments WHERE user_id = ?) as total_investments,
            (SELECT COALESCE(SUM(amount), 0) FROM user_investments WHERE user_id = ? AND status IN ('active', 'completed')) as total_invested,
            (SELECT COALESCE(SUM(total_earned), 0) FROM user_investments WHERE user_id = ?) as total_earned,
            (SELECT COUNT(*) FROM users WHERE referrer_id = ?) as total_referrals,
            (SELECT COALESCE(SUM(amount), 0) FROM referral_commissions WHERE user_id = ?) as referral_earnings
    ");
    $stats_stmt->execute([$user['id'], $user['id'], $user['id'], $user['id'], $user['id']]);
    $user_stats = $stats_stmt->fetch();
} catch (PDOException $e) {
    $user_stats = [
        'total_investments' => 0,
        'total_invested' => 0,
        'total_earned' => 0,
        'total_referrals' => 0,
        'referral_earnings' => 0
    ];
}

$page_title = __('profile') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('profile'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
            </div>
        </header>
        
        <!-- Profile Content -->
        <div class="dashboard-content">
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo e($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo e($success); ?>
                </div>
            <?php endif; ?>
            
            <!-- Profile Overview -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Обзор профиля</h3>
                </div>
                <div class="card-content">
                    <div class="profile-overview">
                        <div class="profile-avatar">
                            <div class="avatar-circle">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="profile-info">
                                <h4><?php echo e($user['first_name'] . ' ' . $user['last_name']); ?></h4>
                                <p class="profile-username">@<?php echo e($user['username']); ?></p>
                                <p class="profile-email"><?php echo e($user['email']); ?></p>
                                <p class="profile-joined">
                                    Зарегистрирован: <?php echo formatDate($user['created_at'], 'd.m.Y'); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="profile-stats">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $user_stats['total_investments']; ?></div>
                                <div class="stat-label">Инвестиций</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">$<?php echo formatAmount($user_stats['total_invested'], 2); ?></div>
                                <div class="stat-label">Инвестировано</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">$<?php echo formatAmount($user_stats['total_earned'], 2); ?></div>
                                <div class="stat-label">Заработано</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value"><?php echo $user_stats['total_referrals']; ?></div>
                                <div class="stat-label">Рефералов</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Settings Tabs -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="tabs-nav">
                        <button class="tab-btn active" onclick="switchTab('personal-info')">
                            <i class="fas fa-user"></i>
                            Личная информация
                        </button>
                        <button class="tab-btn" onclick="switchTab('security')">
                            <i class="fas fa-shield-alt"></i>
                            Безопасность
                        </button>
                        <button class="tab-btn" onclick="switchTab('notifications')">
                            <i class="fas fa-bell"></i>
                            Уведомления
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <!-- Personal Info Tab -->
                    <div id="personal-info" class="tab-content active">
                        <form method="POST" class="profile-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="first_name">Имя</label>
                                    <input type="text"
                                           id="first_name"
                                           name="first_name"
                                           value="<?php echo e($user['first_name']); ?>"
                                           required>
                                </div>

                                <div class="form-group">
                                    <label for="last_name">Фамилия</label>
                                    <input type="text"
                                           id="last_name"
                                           name="last_name"
                                           value="<?php echo e($user['last_name']); ?>"
                                           required>
                                </div>

                                <div class="form-group">
                                    <label for="email">Email</label>
                                    <input type="email"
                                           id="email"
                                           name="email"
                                           value="<?php echo e($user['email']); ?>"
                                           required>
                                </div>

                                <div class="form-group">
                                    <label for="phone">Телефон</label>
                                    <input type="tel"
                                           id="phone"
                                           name="phone"
                                           value="<?php echo e($user['phone']); ?>"
                                           placeholder="+1234567890">
                                </div>

                                <div class="form-group">
                                    <label for="country">Страна</label>
                                    <input type="text"
                                           id="country"
                                           name="country"
                                           value="<?php echo e($user['country']); ?>"
                                           placeholder="Россия">
                                </div>

                                <div class="form-group">
                                    <label for="city">Город</label>
                                    <input type="text"
                                           id="city"
                                           name="city"
                                           value="<?php echo e($user['city']); ?>"
                                           placeholder="Москва">
                                </div>

                                <div class="form-group">
                                    <label for="language">Язык</label>
                                    <select id="language" name="language">
                                        <option value="en" <?php echo $user['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                        <option value="ru" <?php echo $user['language'] === 'ru' ? 'selected' : ''; ?>>Русский</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Сохранить изменения
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Security Tab -->
                    <div id="security" class="tab-content">
                        <div class="security-section">
                            <h4>Смена пароля</h4>
                            <form method="POST" class="password-form">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                                <div class="form-group">
                                    <label for="current_password">Текущий пароль</label>
                                    <input type="password"
                                           id="current_password"
                                           name="current_password"
                                           placeholder="Введите текущий пароль"
                                           required>
                                </div>

                                <div class="form-group">
                                    <label for="new_password">Новый пароль</label>
                                    <input type="password"
                                           id="new_password"
                                           name="new_password"
                                           placeholder="Введите новый пароль"
                                           minlength="6"
                                           required>
                                    <div class="form-help">Минимум 6 символов</div>
                                </div>

                                <div class="form-group">
                                    <label for="confirm_password">Подтвердите пароль</label>
                                    <input type="password"
                                           id="confirm_password"
                                           name="confirm_password"
                                           placeholder="Повторите новый пароль"
                                           required>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" name="change_password" class="btn btn-primary">
                                        <i class="fas fa-key"></i>
                                        Изменить пароль
                                    </button>
                                </div>
                            </form>
                        </div>

                        <div class="security-info">
                            <h4>Информация о безопасности</h4>
                            <div class="security-items">
                                <div class="security-item">
                                    <div class="security-icon">
                                        <i class="fas fa-shield-check text-success"></i>
                                    </div>
                                    <div class="security-details">
                                        <h5>Двухфакторная аутентификация</h5>
                                        <p>Не настроена</p>
                                        <button class="btn btn-outline btn-sm">Настроить</button>
                                    </div>
                                </div>

                                <div class="security-item">
                                    <div class="security-icon">
                                        <i class="fas fa-clock text-info"></i>
                                    </div>
                                    <div class="security-details">
                                        <h5>Последний вход</h5>
                                        <p><?php echo formatDate($user['last_login'], 'd.m.Y H:i'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications Tab -->
                    <div id="notifications" class="tab-content">
                        <form method="POST" class="notifications-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <div class="notifications-section">
                                <h4>Настройки уведомлений</h4>

                                <div class="notification-item">
                                    <div class="notification-info">
                                        <h5>Email уведомления</h5>
                                        <p>Получать уведомления о транзакциях и важных событиях на email</p>
                                    </div>
                                    <div class="notification-toggle">
                                        <label class="switch">
                                            <input type="checkbox"
                                                   name="email_notifications"
                                                   <?php echo $user['email_notifications'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="notification-item">
                                    <div class="notification-info">
                                        <h5>SMS уведомления</h5>
                                        <p>Получать SMS о важных операциях и изменениях в аккаунте</p>
                                    </div>
                                    <div class="notification-toggle">
                                        <label class="switch">
                                            <input type="checkbox"
                                                   name="sms_notifications"
                                                   <?php echo $user['sms_notifications'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="notification-item">
                                    <div class="notification-info">
                                        <h5>Push уведомления</h5>
                                        <p>Получать push-уведомления в браузере</p>
                                    </div>
                                    <div class="notification-toggle">
                                        <label class="switch">
                                            <input type="checkbox"
                                                   name="push_notifications"
                                                   <?php echo $user['push_notifications'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="update_notifications" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Сохранить настройки
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <script>
        // Переключение вкладок
        function switchTab(tabId) {
            // Скрываем все вкладки
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Убираем активный класс с кнопок
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Показываем выбранную вкладку
            document.getElementById(tabId).classList.add('active');

            // Активируем соответствующую кнопку
            event.target.classList.add('active');
        }

        // Валидация формы смены пароля
        document.querySelector('.password-form').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (newPassword.length < 6) {
                e.preventDefault();
                showNotification('Новый пароль должен содержать минимум 6 символов', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                showNotification('Пароли не совпадают', 'error');
                return;
            }
        });

        // Валидация email
        document.getElementById('email').addEventListener('blur', function() {
            const email = this.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (email && !emailRegex.test(email)) {
                this.style.borderColor = '#f6465d';
                showNotification('Неверный формат email', 'error');
            } else {
                this.style.borderColor = '';
            }
        });

        // Валидация телефона
        document.getElementById('phone').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.length <= 11) {
                    value = value.replace(/(\d{1})(\d{3})(\d{3})(\d{2})(\d{2})/, '+$1 ($2) $3-$4-$5');
                }
                this.value = value;
            }
        });

        // Подтверждение изменений профиля
        document.querySelector('.profile-form').addEventListener('submit', function(e) {
            if (!confirm('Вы уверены, что хотите сохранить изменения?')) {
                e.preventDefault();
            }
        });

        // Подтверждение изменения настроек уведомлений
        document.querySelector('.notifications-form').addEventListener('submit', function(e) {
            if (!confirm('Сохранить настройки уведомлений?')) {
                e.preventDefault();
            }
        });

        // Анимация переключателей
        document.querySelectorAll('.switch input').forEach(input => {
            input.addEventListener('change', function() {
                const slider = this.nextElementSibling;
                if (this.checked) {
                    slider.style.backgroundColor = '#0ea5e9';
                } else {
                    slider.style.backgroundColor = '#cbd5e1';
                }
            });
        });

        // Инициализация состояния переключателей
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.switch input').forEach(input => {
                const slider = input.nextElementSibling;
                if (input.checked) {
                    slider.style.backgroundColor = '#0ea5e9';
                } else {
                    slider.style.backgroundColor = '#cbd5e1';
                }
            });
        });
    </script>
</body>
</html>
