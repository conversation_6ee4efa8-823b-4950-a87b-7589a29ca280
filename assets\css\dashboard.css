/**
 * AstroGenix Dashboard Styles
 * Стили для панели управления пользователя
 */

/* Dashboard Layout */
.dashboard-page {
    display: flex;
    min-height: 100vh;
    background: var(--bg-primary);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--z-fixed);
    transform: translateX(-100%);
    transition: var(--transition-base);
}

.sidebar.active {
    transform: translateX(0);
}

@media (min-width: 1024px) {
    .sidebar {
        position: static;
        transform: translateX(0);
    }
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.sidebar-logo img {
    height: 32px;
}

.sidebar-logo span {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-xs);
    display: none;
}

@media (max-width: 1023px) {
    .sidebar-toggle {
        display: block;
    }
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--text-primary);
    background: rgba(240, 185, 11, 0.1);
}

.nav-item.active .nav-link {
    color: var(--primary-color);
    background: rgba(240, 185, 11, 0.1);
}

.nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-lg);
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(246, 70, 93, 0.1);
    border: 1px solid rgba(246, 70, 93, 0.2);
    border-radius: var(--radius-md);
    color: var(--danger-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.logout-btn:hover {
    background: rgba(246, 70, 93, 0.2);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 0;
    display: flex;
    flex-direction: column;
}

@media (min-width: 1024px) {
    .main-content {
        margin-left: 280px;
    }
}

/* Dashboard Header */
.dashboard-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.mobile-menu-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-xs);
    display: block;
}

@media (min-width: 1024px) {
    .mobile-menu-toggle {
        display: none;
    }
}

.dashboard-header h1 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.balance-display {
    text-align: right;
}

.balance-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.balance-amount {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

@media (max-width: 768px) {
    .header-actions {
        display: none;
    }
}

.notifications-dropdown {
    position: relative;
}

.notifications-toggle {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-sm);
    position: relative;
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--danger-color);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: var(--transition-base);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-xl);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-action {
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.card-action:hover {
    text-decoration: underline;
}

.card-content {
    padding: var(--spacing-lg);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl) var(--spacing-lg);
    color: var(--text-muted);
}

.empty-state i {
    font-size: var(--font-size-4xl);
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
}

/* Investments List */
.investments-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.investment-item {
    padding: var(--spacing-md);
    background: var(--bg-input);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.investment-info {
    flex: 1;
}

.investment-plan {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.investment-amount {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.investment-stats {
    text-align: right;
}

.daily-profit {
    color: var(--success-color);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.total-earned {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Transactions List */
.transactions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.transaction-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-input);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.transaction-info {
    flex: 1;
}

.transaction-type {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    text-transform: capitalize;
}

.transaction-date {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.transaction-amount {
    font-weight: 700;
    margin-right: var(--spacing-md);
}

.transaction-amount.positive {
    color: var(--success-color);
}

.transaction-amount.negative {
    color: var(--danger-color);
}

.transaction-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: rgba(252, 213, 53, 0.2);
    color: var(--warning-color);
}

.status-approved {
    background: rgba(0, 212, 170, 0.2);
    color: var(--success-color);
}

.status-rejected {
    background: rgba(246, 70, 93, 0.2);
    color: var(--danger-color);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.quick-action {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-input);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    text-decoration: none;
    transition: var(--transition-base);
}

.quick-action:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
}

.action-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-secondary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-lg);
}

.action-text {
    flex: 1;
}

.action-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.action-desc {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Referral Stats */
.referral-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.referral-stat {
    text-align: center;
}

.referral-stat .stat-number {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.referral-stat .stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.referral-link-section {
    margin-top: var(--spacing-lg);
}

.referral-link-section label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.referral-link-input {
    display: flex;
    gap: var(--spacing-sm);
}

.referral-link-input input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.copy-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-inverse);
    cursor: pointer;
    transition: var(--transition-fast);
}

.copy-btn:hover {
    background: var(--primary-dark);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-content {
        padding: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .dashboard-grid {
        gap: var(--spacing-md);
    }
    
    .referral-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .investment-item,
    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .investment-stats,
    .transaction-amount {
        align-self: flex-end;
    }
}

/* Investment Page Styles */
.plans-section {
    margin-bottom: var(--spacing-2xl);
}

.plan-card {
    cursor: pointer;
    transition: var(--transition-base);
}

.plan-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(240, 185, 11, 0.2);
    transform: translateY(-2px);
}

.plan-calculator {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(240, 185, 11, 0.05);
    border: 1px solid rgba(240, 185, 11, 0.1);
    border-radius: var(--radius-md);
}

.calc-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.calc-row:last-child {
    margin-bottom: 0;
    font-weight: 600;
}

.calc-value {
    color: var(--text-primary);
}

.calc-value.profit {
    color: var(--success-color);
}

.investment-form-section {
    max-width: 600px;
    margin: 0 auto;
}

.amount-input {
    position: relative;
}

.amount-input input {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 600;
    text-align: center;
}

.amount-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.amount-btn {
    flex: 1;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.amount-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.amount-info {
    margin-top: var(--spacing-sm);
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.investment-preview {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-lg);
    background: var(--bg-input);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
}

.preview-header {
    margin-bottom: var(--spacing-md);
}

.preview-header h4 {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    margin: 0;
}

.preview-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.preview-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.preview-row:last-child {
    border-bottom: none;
    font-weight: 600;
}

.preview-row .profit {
    color: var(--success-color);
    font-weight: 600;
}

/* Alert Styles */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.alert-error {
    background: rgba(246, 70, 93, 0.1);
    border: 1px solid rgba(246, 70, 93, 0.2);
    color: var(--danger-color);
}

.alert-success {
    background: rgba(0, 212, 170, 0.1);
    border: 1px solid rgba(0, 212, 170, 0.2);
    color: var(--success-color);
}

.alert-info {
    background: rgba(24, 144, 255, 0.1);
    border: 1px solid rgba(24, 144, 255, 0.2);
    color: var(--info-color);
}

.alert-warning {
    background: rgba(252, 213, 53, 0.1);
    border: 1px solid rgba(252, 213, 53, 0.2);
    color: var(--warning-color);
}
