/**
 * AstroGenix Authentication JavaScript
 * Функции для страниц авторизации и регистрации
 */

document.addEventListener('DOMContentLoaded', function() {
    initPasswordStrength();
    initPasswordToggle();
    initFormValidation();
    initCountrySelect();
    
    console.log('Auth page initialized');
});

/**
 * Индикатор силы пароля
 */
function initPasswordStrength() {
    const passwordInput = document.getElementById('password');
    const strengthIndicator = document.getElementById('password-strength');
    
    if (!passwordInput || !strengthIndicator) return;
    
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        
        strengthIndicator.className = 'password-strength';
        
        if (password.length === 0) {
            return;
        }
        
        if (strength < 30) {
            strengthIndicator.classList.add('weak');
        } else if (strength < 70) {
            strengthIndicator.classList.add('medium');
        } else {
            strengthIndicator.classList.add('strong');
        }
    });
}

/**
 * Расчет силы пароля
 */
function calculatePasswordStrength(password) {
    let strength = 0;
    
    // Длина пароля
    if (password.length >= 8) strength += 25;
    if (password.length >= 12) strength += 15;
    
    // Содержит строчные буквы
    if (/[a-z]/.test(password)) strength += 15;
    
    // Содержит заглавные буквы
    if (/[A-Z]/.test(password)) strength += 15;
    
    // Содержит цифры
    if (/[0-9]/.test(password)) strength += 15;
    
    // Содержит специальные символы
    if (/[^A-Za-z0-9]/.test(password)) strength += 15;
    
    return Math.min(strength, 100);
}

/**
 * Переключение видимости пароля
 */
function initPasswordToggle() {
    document.querySelectorAll('.password-toggle').forEach(button => {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
}

function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle');
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

/**
 * Валидация форм
 */
function initFormValidation() {
    const forms = document.querySelectorAll('.auth-form');
    
    forms.forEach(form => {
        // Валидация в реальном времени
        const inputs = form.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('error')) {
                    validateField(this);
                }
            });
        });
        
        // Валидация при отправке
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });
            
            // Дополнительная валидация для регистрации
            if (form.id === 'register-form') {
                const password = form.querySelector('#password');
                const confirmPassword = form.querySelector('#confirm_password');
                const agreeTerms = form.querySelector('input[name="agree_terms"]');
                
                if (password && confirmPassword && password.value !== confirmPassword.value) {
                    showFieldError(confirmPassword, 'Пароли не совпадают');
                    isValid = false;
                }
                
                if (agreeTerms && !agreeTerms.checked) {
                    showNotification('Вы должны согласиться с условиями использования', 'error');
                    isValid = false;
                }
            }
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Пожалуйста, исправьте ошибки в форме', 'error');
            } else {
                // Показать индикатор загрузки
                const submitButton = form.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<div class="spinner"></div> Обработка...';
                }
            }
        });
    });
}

/**
 * Валидация отдельного поля
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Проверка обязательных полей
    if (field.hasAttribute('required') && !value) {
        errorMessage = 'Это поле обязательно для заполнения';
        isValid = false;
    }
    
    // Валидация email
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            errorMessage = 'Введите корректный email адрес';
            isValid = false;
        }
    }
    
    // Валидация пароля
    if (field.type === 'password' && field.name === 'password' && value) {
        if (value.length < 6) {
            errorMessage = 'Пароль должен содержать минимум 6 символов';
            isValid = false;
        }
    }
    
    // Валидация имени пользователя
    if (field.name === 'username' && value) {
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        if (!usernameRegex.test(value)) {
            errorMessage = 'Имя пользователя должно содержать 3-20 символов (буквы, цифры, _)';
            isValid = false;
        }
    }
    
    // Валидация телефона
    if (field.type === 'tel' && value) {
        const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            errorMessage = 'Введите корректный номер телефона';
            isValid = false;
        }
    }
    
    if (isValid) {
        clearFieldError(field);
        field.parentElement.classList.add('success');
    } else {
        showFieldError(field, errorMessage);
        field.parentElement.classList.remove('success');
    }
    
    return isValid;
}

/**
 * Показать ошибку поля
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('error');
    
    const error = document.createElement('div');
    error.className = 'field-error';
    error.textContent = message;
    
    field.parentElement.appendChild(error);
}

/**
 * Очистить ошибку поля
 */
function clearFieldError(field) {
    field.classList.remove('error');
    
    const existingError = field.parentElement.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * Инициализация выбора страны
 */
function initCountrySelect() {
    const countrySelect = document.getElementById('country');
    if (!countrySelect) return;
    
    // Автоопределение страны по IP (заглушка)
    // В реальном проекте можно использовать API для определения страны
    detectUserCountry().then(country => {
        if (country && !countrySelect.value) {
            countrySelect.value = country;
        }
    });
}

/**
 * Определение страны пользователя
 */
async function detectUserCountry() {
    try {
        // Заглушка - в реальном проекте использовать API
        return 'US';
    } catch (error) {
        console.error('Error detecting country:', error);
        return null;
    }
}

/**
 * Проверка доступности имени пользователя
 */
function checkUsernameAvailability(username) {
    if (username.length < 3) return;
    
    fetch('api/check-username.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: username })
    })
    .then(response => response.json())
    .then(data => {
        const usernameField = document.getElementById('username');
        const feedback = document.createElement('div');
        feedback.className = 'username-feedback';
        
        // Удаляем предыдущий feedback
        const existingFeedback = usernameField.parentElement.querySelector('.username-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }
        
        if (data.available) {
            feedback.innerHTML = '<i class="fas fa-check"></i> Имя пользователя доступно';
            feedback.classList.add('success');
        } else {
            feedback.innerHTML = '<i class="fas fa-times"></i> Имя пользователя уже занято';
            feedback.classList.add('error');
        }
        
        usernameField.parentElement.appendChild(feedback);
    })
    .catch(error => {
        console.error('Error checking username:', error);
    });
}

/**
 * Автозаполнение формы (для тестирования)
 */
function fillTestData() {
    const form = document.querySelector('.auth-form');
    if (!form) return;
    
    const testData = {
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe' + Math.floor(Math.random() * 1000),
        email: 'john.doe' + Math.floor(Math.random() * 1000) + '@example.com',
        phone: '+1234567890',
        country: 'US',
        password: 'password123',
        confirm_password: 'password123'
    };
    
    Object.keys(testData).forEach(key => {
        const field = form.querySelector(`[name="${key}"]`);
        if (field) {
            field.value = testData[key];
        }
    });
    
    const agreeTerms = form.querySelector('input[name="agree_terms"]');
    if (agreeTerms) {
        agreeTerms.checked = true;
    }
}

/**
 * Социальная авторизация
 */
function socialLogin(provider) {
    const popup = window.open(
        `auth/social.php?provider=${provider}`,
        'social_login',
        'width=500,height=600,scrollbars=yes,resizable=yes'
    );
    
    // Отслеживание закрытия popup
    const checkClosed = setInterval(() => {
        if (popup.closed) {
            clearInterval(checkClosed);
            // Проверяем, была ли успешная авторизация
            checkAuthStatus();
        }
    }, 1000);
}

/**
 * Проверка статуса авторизации
 */
function checkAuthStatus() {
    fetch('api/auth-status.php')
        .then(response => response.json())
        .then(data => {
            if (data.authenticated) {
                window.location.href = 'dashboard.php';
            }
        })
        .catch(error => {
            console.error('Error checking auth status:', error);
        });
}

/**
 * Двухфакторная аутентификация
 */
function initTwoFactor() {
    const inputs = document.querySelectorAll('.two-factor-input input');
    
    inputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            if (this.value.length === 1 && index < inputs.length - 1) {
                inputs[index + 1].focus();
            }
        });
        
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && this.value === '' && index > 0) {
                inputs[index - 1].focus();
            }
        });
    });
}

/**
 * Восстановление пароля
 */
function initPasswordReset() {
    const resetForm = document.getElementById('reset-form');
    if (!resetForm) return;
    
    resetForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = this.querySelector('input[name="email"]').value;
        const submitButton = this.querySelector('button[type="submit"]');
        
        submitButton.disabled = true;
        submitButton.innerHTML = '<div class="spinner"></div> Отправка...';
        
        fetch('api/password-reset.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Инструкции по восстановлению пароля отправлены на ваш email', 'success');
                resetForm.reset();
            } else {
                showNotification(data.message || 'Ошибка при отправке', 'error');
            }
        })
        .catch(error => {
            showNotification('Произошла ошибка. Попробуйте позже.', 'error');
        })
        .finally(() => {
            submitButton.disabled = false;
            submitButton.innerHTML = 'Восстановить пароль';
        });
    });
}

// Горячие клавиши для разработки
document.addEventListener('keydown', function(e) {
    // Ctrl + Shift + T - заполнить тестовыми данными
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        fillTestData();
    }
});
