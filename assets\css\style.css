/**
 * AstroGenix Styles
 * Современный дизайн в стиле Binance/Coinbase
 */

/* CSS Variables */
:root {
    /* Colors */
    --primary-color: #f0b90b;
    --primary-dark: #d9a441;
    --secondary-color: #1e2329;
    --accent-color: #00d4aa;
    --danger-color: #f6465d;
    --warning-color: #fcd535;
    --success-color: #00d4aa;
    --info-color: #1890ff;
    
    /* Background Colors */
    --bg-primary: #0b0e11;
    --bg-secondary: #1e2329;
    --bg-tertiary: #2b3139;
    --bg-card: #1e2329;
    --bg-input: #2b3139;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b7bdc6;
    --text-muted: #848e9c;
    --text-inverse: #0b0e11;
    
    /* Border Colors */
    --border-color: #2b3139;
    --border-light: #3c4043;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #f0b90b 0%, #d9a441 100%);
    --gradient-secondary: linear-gradient(135deg, #00d4aa 0%, #1890ff 100%);
    --gradient-dark: linear-gradient(135deg, #1e2329 0%, #2b3139 100%);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-base);
    white-space: nowrap;
    user-select: none;
    min-height: 40px;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(240, 185, 11, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
    border-color: var(--success-color);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--text-inverse);
    border-color: var(--danger-color);
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
    min-height: 32px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-height: 48px;
}

.btn-block {
    width: 100%;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(11, 14, 17, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: var(--z-fixed);
    transition: var(--transition-base);
}

.navbar {
    padding: var(--spacing-md) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.navbar-brand .logo {
    height: 32px;
    margin-right: var(--spacing-sm);
}

.brand-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-menu {
    display: none;
}

@media (min-width: 768px) {
    .navbar-menu {
        display: block;
    }
}

.navbar-nav {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-secondary);
    font-weight: 500;
    padding: var(--spacing-sm) 0;
    position: relative;
    transition: var(--transition-fast);
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-base);
}

.nav-link:hover::after {
    width: 100%;
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.language-switcher select {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.navbar-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    padding: var(--spacing-sm);
}

@media (min-width: 768px) {
    .navbar-toggle {
        display: none;
    }
}

.navbar-toggle span {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-3xl) 0;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(240, 185, 11, 0.1) 0%, transparent 70%);
}

.hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(240, 185, 11, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 212, 170, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

@media (min-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr 1fr;
    }
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
}

@media (min-width: 768px) {
    .hero-title {
        font-size: 3.5rem;
    }
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Hero Card */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-xl);
    max-width: 400px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.hero-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
}

.card-header {
    margin-bottom: var(--spacing-lg);
}

.card-title {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.card-balance {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
}

.card-chart {
    height: 120px;
    margin-bottom: var(--spacing-lg);
}

.card-stats {
    display: flex;
    justify-content: space-between;
}

.card-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card-stat .stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.card-stat .stat-value {
    font-weight: 600;
}

.card-stat .stat-value.positive {
    color: var(--success-color);
}

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.section-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Investment Plans */
.plans-section {
    padding: var(--spacing-3xl) 0;
    background: var(--bg-secondary);
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.plan-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    position: relative;
    transition: var(--transition-base);
}

.plan-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.plan-card.featured {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.plan-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.plan-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.plan-name {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.plan-profit {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.plan-profit-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.plan-features {
    margin-bottom: var(--spacing-xl);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.feature i {
    color: var(--primary-color);
    width: 20px;
}

.plan-description {
    color: var(--text-muted);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

/* Features Section */
.features-section {
    padding: var(--spacing-3xl) 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: var(--transition-base);
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--text-inverse);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.feature-description {
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 767px) {
    .hero-actions {
        flex-direction: column;
    }
    
    .hero-actions .btn {
        width: 100%;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
