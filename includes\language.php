<?php
/**
 * Language Management System for AstroGenix
 */

class Language {
    private static $instance = null;
    private $currentLanguage = 'en';
    private $translations = [];
    private $availableLanguages = [
        'en' => 'English',
        'ru' => 'Русский'
    ];

    private function __construct() {
        $this->initializeLanguage();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function initializeLanguage() {
        // Check for language in session
        if (isset($_SESSION['language']) && array_key_exists($_SESSION['language'], $this->availableLanguages)) {
            $this->currentLanguage = $_SESSION['language'];
        }
        // Check for language in cookie
        elseif (isset($_COOKIE['language']) && array_key_exists($_COOKIE['language'], $this->availableLanguages)) {
            $this->currentLanguage = $_COOKIE['language'];
            $_SESSION['language'] = $this->currentLanguage;
        }
        // Check for language in URL parameter
        elseif (isset($_GET['lang']) && array_key_exists($_GET['lang'], $this->availableLanguages)) {
            $this->setLanguage($_GET['lang']);
        }
        // Default to English
        else {
            $this->currentLanguage = 'en';
            $_SESSION['language'] = $this->currentLanguage;
        }

        $this->loadTranslations();
    }

    private function loadTranslations() {
        $languageFile = __DIR__ . '/../languages/' . $this->currentLanguage . '.php';
        if (file_exists($languageFile)) {
            $this->translations = include $languageFile;
        } else {
            // Fallback to English if language file doesn't exist
            $fallbackFile = __DIR__ . '/../languages/en.php';
            if (file_exists($fallbackFile)) {
                $this->translations = include $fallbackFile;
            }
        }
    }

    public function setLanguage($language) {
        if (array_key_exists($language, $this->availableLanguages)) {
            $this->currentLanguage = $language;
            $_SESSION['language'] = $language;
            
            // Set cookie for 30 days
            setcookie('language', $language, time() + (30 * 24 * 60 * 60), '/');
            
            $this->loadTranslations();
            return true;
        }
        return false;
    }

    public function getCurrentLanguage() {
        return $this->currentLanguage;
    }

    public function getAvailableLanguages() {
        return $this->availableLanguages;
    }

    public function translate($key, $params = []) {
        $translation = $this->translations[$key] ?? $key;
        
        // Replace parameters in translation
        if (!empty($params)) {
            foreach ($params as $param => $value) {
                $translation = str_replace(':' . $param, $value, $translation);
            }
        }
        
        return $translation;
    }

    public function get($key, $params = []) {
        return $this->translate($key, $params);
    }
}

// Global helper function
function __($key, $params = []) {
    return Language::getInstance()->translate($key, $params);
}

function lang($key, $params = []) {
    return Language::getInstance()->translate($key, $params);
}

function getCurrentLanguage() {
    return Language::getInstance()->getCurrentLanguage();
}

function getAvailableLanguages() {
    return Language::getInstance()->getAvailableLanguages();
}

function setLanguage($language) {
    return Language::getInstance()->setLanguage($language);
}

// Initialize language system
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$language = Language::getInstance();
?>
