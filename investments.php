<?php
/**
 * AstroGenix - Investments Page
 * Страница управления инвестициями пользователя
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

// Параметры фильтрации и сортировки
$status_filter = $_GET['status'] ?? 'all';
$plan_filter = $_GET['plan'] ?? 'all';
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Построение WHERE условий
$where_conditions = ['ui.user_id = ?'];
$params = [$user['id']];

if ($status_filter !== 'all') {
    $where_conditions[] = 'ui.status = ?';
    $params[] = $status_filter;
}

if ($plan_filter !== 'all') {
    $where_conditions[] = 'ui.plan_id = ?';
    $params[] = $plan_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Валидация сортировки
$allowed_sort = ['created_at', 'amount', 'daily_profit', 'total_earned', 'end_date'];
$sort_by = in_array($sort_by, $allowed_sort) ? $sort_by : 'created_at';
$sort_order = in_array($sort_order, ['ASC', 'DESC']) ? $sort_order : 'DESC';

try {
    // Получение инвестиций с пагинацией
    $stmt = $pdo->prepare("
        SELECT ui.*, ip.name as plan_name, ip.daily_profit as plan_daily_profit, 
               ip.duration_days, ip.total_return,
               DATEDIFF(ui.end_date, NOW()) as days_left,
               CASE 
                   WHEN ui.status = 'active' AND ui.end_date > NOW() THEN 'active'
                   WHEN ui.status = 'active' AND ui.end_date <= NOW() THEN 'completed'
                   ELSE ui.status
               END as actual_status
        FROM user_investments ui 
        JOIN investment_plans ip ON ui.plan_id = ip.id 
        WHERE {$where_clause}
        ORDER BY ui.{$sort_by} {$sort_order}
        LIMIT {$per_page} OFFSET {$offset}
    ");
    $stmt->execute($params);
    $investments = $stmt->fetchAll();
    
    // Подсчет общего количества для пагинации
    $count_stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM user_investments ui 
        JOIN investment_plans ip ON ui.plan_id = ip.id 
        WHERE {$where_clause}
    ");
    $count_stmt->execute($params);
    $total_investments = $count_stmt->fetchColumn();
    $total_pages = ceil($total_investments / $per_page);
    
    // Получение планов для фильтра
    $plans_stmt = $pdo->query("SELECT id, name FROM investment_plans WHERE status = 'active' ORDER BY sort_order");
    $plans = $plans_stmt->fetchAll();
    
    // Статистика инвестиций
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_count,
            COALESCE(SUM(amount), 0) as total_invested,
            COALESCE(SUM(total_earned), 0) as total_earned,
            COALESCE(SUM(CASE WHEN status = 'active' THEN amount ELSE 0 END), 0) as active_amount,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
        FROM user_investments 
        WHERE user_id = ?
    ");
    $stats_stmt->execute([$user['id']]);
    $stats = $stats_stmt->fetch();
    
} catch (PDOException $e) {
    $investments = [];
    $plans = [];
    $stats = [
        'total_count' => 0,
        'total_invested' => 0,
        'total_earned' => 0,
        'active_amount' => 0,
        'active_count' => 0
    ];
    $total_pages = 1;
}

$page_title = __('investments') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('my_investments'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
                
                <div class="header-actions">
                    <a href="invest.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        <?php echo __('invest_now'); ?>
                    </a>
                </div>
            </div>
        </header>
        
        <!-- Investments Content -->
        <div class="dashboard-content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $stats['total_count']; ?></div>
                        <div class="stat-label">Всего инвестиций</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($stats['total_invested'], 2); ?></div>
                        <div class="stat-label"><?php echo __('total_invested'); ?></div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($stats['total_earned'], 2); ?></div>
                        <div class="stat-label"><?php echo __('total_earned'); ?></div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $stats['active_count']; ?></div>
                        <div class="stat-label"><?php echo __('active_investments'); ?></div>
                    </div>
                </div>
            </div>

            <!-- Filters and Controls -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Фильтры и сортировка</h3>
                </div>
                <div class="card-content">
                    <form method="GET" class="filters-form">
                        <div class="filters-grid">
                            <div class="filter-group">
                                <label for="status">Статус:</label>
                                <select name="status" id="status">
                                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Все</option>
                                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Активные</option>
                                    <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Завершенные</option>
                                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Отмененные</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="plan">План:</label>
                                <select name="plan" id="plan">
                                    <option value="all" <?php echo $plan_filter === 'all' ? 'selected' : ''; ?>>Все планы</option>
                                    <?php foreach ($plans as $plan): ?>
                                        <option value="<?php echo $plan['id']; ?>" <?php echo $plan_filter == $plan['id'] ? 'selected' : ''; ?>>
                                            <?php echo e($plan['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="sort">Сортировка:</label>
                                <select name="sort" id="sort">
                                    <option value="created_at" <?php echo $sort_by === 'created_at' ? 'selected' : ''; ?>>По дате создания</option>
                                    <option value="amount" <?php echo $sort_by === 'amount' ? 'selected' : ''; ?>>По сумме</option>
                                    <option value="daily_profit" <?php echo $sort_by === 'daily_profit' ? 'selected' : ''; ?>>По прибыли</option>
                                    <option value="total_earned" <?php echo $sort_by === 'total_earned' ? 'selected' : ''; ?>>По заработку</option>
                                    <option value="end_date" <?php echo $sort_by === 'end_date' ? 'selected' : ''; ?>>По дате окончания</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label for="order">Порядок:</label>
                                <select name="order" id="order">
                                    <option value="DESC" <?php echo $sort_order === 'DESC' ? 'selected' : ''; ?>>По убыванию</option>
                                    <option value="ASC" <?php echo $sort_order === 'ASC' ? 'selected' : ''; ?>>По возрастанию</option>
                                </select>
                            </div>

                            <div class="filter-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i>
                                    Применить
                                </button>
                                <a href="investments.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    Сбросить
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Investments List -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Список инвестиций</h3>
                    <div class="card-actions">
                        <span class="results-count">
                            Показано <?php echo count($investments); ?> из <?php echo $total_investments; ?>
                        </span>
                    </div>
                </div>
                <div class="card-content">
                    <?php if (empty($investments)): ?>
                        <div class="empty-state">
                            <i class="fas fa-chart-line"></i>
                            <h3>Инвестиций не найдено</h3>
                            <p>У вас пока нет инвестиций или они не соответствуют выбранным фильтрам</p>
                            <a href="invest.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Создать инвестицию
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="investments-table-container">
                            <table class="investments-table">
                                <thead>
                                    <tr>
                                        <th>План</th>
                                        <th>Сумма</th>
                                        <th>Ежедневно</th>
                                        <th>Заработано</th>
                                        <th>Прогресс</th>
                                        <th>Статус</th>
                                        <th>Дата создания</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($investments as $investment): ?>
                                        <tr class="investment-row" data-status="<?php echo $investment['actual_status']; ?>">
                                            <td>
                                                <div class="plan-info">
                                                    <div class="plan-name"><?php echo e($investment['plan_name']); ?></div>
                                                    <div class="plan-rate"><?php echo $investment['plan_daily_profit']; ?>% в день</div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="amount-info">
                                                    <div class="amount">$<?php echo formatAmount($investment['amount'], 2); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="daily-profit">
                                                    +$<?php echo formatAmount($investment['daily_profit'], 2); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="total-earned">
                                                    $<?php echo formatAmount($investment['total_earned'], 2); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="progress-info">
                                                    <?php
                                                    $progress = 0;
                                                    if ($investment['actual_status'] === 'active' && $investment['days_left'] !== null) {
                                                        $total_days = $investment['duration_days'];
                                                        $elapsed_days = $total_days - max(0, $investment['days_left']);
                                                        $progress = min(100, ($elapsed_days / $total_days) * 100);
                                                    } elseif ($investment['actual_status'] === 'completed') {
                                                        $progress = 100;
                                                    }
                                                    ?>
                                                    <div class="progress-bar">
                                                        <div class="progress-fill" style="width: <?php echo $progress; ?>%"></div>
                                                    </div>
                                                    <div class="progress-text">
                                                        <?php if ($investment['actual_status'] === 'active' && $investment['days_left'] > 0): ?>
                                                            <?php echo $investment['days_left']; ?> дней осталось
                                                        <?php elseif ($investment['actual_status'] === 'completed'): ?>
                                                            Завершено
                                                        <?php else: ?>
                                                            <?php echo round($progress); ?>%
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $investment['actual_status']; ?>">
                                                    <?php
                                                    switch($investment['actual_status']) {
                                                        case 'active': echo 'Активная'; break;
                                                        case 'completed': echo 'Завершена'; break;
                                                        case 'cancelled': echo 'Отменена'; break;
                                                        default: echo ucfirst($investment['actual_status']);
                                                    }
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="date-info">
                                                    <div class="date"><?php echo formatDate($investment['created_at'], 'd.m.Y'); ?></div>
                                                    <div class="time"><?php echo formatDate($investment['created_at'], 'H:i'); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="actions">
                                                    <button class="btn-icon" onclick="viewInvestment(<?php echo $investment['id']; ?>)" title="Подробности">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($investment['actual_status'] === 'active'): ?>
                                                        <button class="btn-icon btn-danger" onclick="cancelInvestment(<?php echo $investment['id']; ?>)" title="Отменить">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination-container">
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                                Предыдущая
                            </a>
                        <?php endif; ?>

                        <div class="pagination-pages">
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            if ($start_page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>" class="pagination-page">1</a>
                                <?php if ($start_page > 2): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                   class="pagination-page <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>" class="pagination-page"><?php echo $total_pages; ?></a>
                            <?php endif; ?>
                        </div>

                        <?php if ($page < $total_pages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-btn">
                                Следующая
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Investment Details Modal -->
    <div id="investment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Детали инвестиции</h3>
                <button class="modal-close" onclick="closeModal('investment-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="investment-details">
                <!-- Содержимое загружается через AJAX -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <script>
        // Просмотр деталей инвестиции
        function viewInvestment(investmentId) {
            fetch(`api/get-investment-details.php?id=${investmentId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('investment-details').innerHTML = data.html;
                        openModal('investment-modal');
                    } else {
                        showNotification(data.message || 'Ошибка загрузки данных', 'error');
                    }
                })
                .catch(error => {
                    showNotification('Ошибка соединения', 'error');
                });
        }

        // Отмена инвестиции
        function cancelInvestment(investmentId) {
            if (!confirm('Вы уверены, что хотите отменить эту инвестицию? Это действие нельзя отменить.')) {
                return;
            }

            fetch('api/cancel-investment.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    investment_id: investmentId,
                    csrf_token: '<?php echo generateCSRFToken(); ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Инвестиция отменена', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification(data.message || 'Ошибка отмены инвестиции', 'error');
                }
            })
            .catch(error => {
                showNotification('Ошибка соединения', 'error');
            });
        }

        // Автоматическое применение фильтров при изменении
        document.querySelectorAll('.filters-form select').forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
    </script>
</body>
</html>
