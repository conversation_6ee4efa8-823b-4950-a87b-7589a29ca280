<?php
/**
 * AstroGenix - Main Page
 * Professional USDT Staking Platform
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';
require_once 'includes/language.php';

// Получение статистики для главной страницы
try {
    $stats_query = "
        SELECT 
            (SELECT COUNT(*) FROM users WHERE status = 'active') as total_users,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'approved') as total_deposits,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'withdrawal' AND status = 'approved') as total_withdrawals,
            (SELECT COALESCE(SUM(total_earned), 0) FROM users) as total_profits
    ";
    $stats = $pdo->query($stats_query)->fetch();
    
    // Получение активных инвестиционных планов
    $plans_query = "SELECT * FROM investment_plans WHERE status = 'active' ORDER BY sort_order ASC";
    $plans = $pdo->query($plans_query)->fetchAll();
    
    // Получение последних новостей
    $news_query = "SELECT * FROM news WHERE status = 'published' ORDER BY published_at DESC LIMIT 3";
    $latest_news = $pdo->query($news_query)->fetchAll();
    
} catch (PDOException $e) {
    $stats = ['total_users' => 0, 'total_deposits' => 0, 'total_withdrawals' => 0, 'total_profits' => 0];
    $plans = [];
    $latest_news = [];
}

$page_title = __('hero_title') . ' - ' . __('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars(__('hero_subtitle')); ?>">
    <meta name="keywords" content="USDT, staking, cryptocurrency, investment, passive income">
    <meta name="author" content="AstroGenix">

    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(__('hero_subtitle')); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">

    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="assets/css/modern.css" rel="stylesheet">
    <link href="assets/css/homepage.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="navbar-brand">
                    <a href="<?php echo SITE_URL; ?>" class="brand-link">
                        <img src="assets/images/logo.svg" alt="<?php echo __('site_name'); ?>" class="logo">
                    </a>
                </div>

                <div class="navbar-menu" id="navbar-menu">
                    <ul class="navbar-nav">
                        <li><a href="#home" class="nav-link"><?php echo __('home'); ?></a></li>
                        <li><a href="#plans" class="nav-link"><?php echo __('investment_plans'); ?></a></li>
                        <li><a href="#features" class="nav-link"><?php echo __('about'); ?></a></li>
                        <li><a href="#contact" class="nav-link"><?php echo __('contact'); ?></a></li>
                    </ul>
                </div>

                <div class="navbar-actions">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="dashboard.php" class="btn btn-primary"><?php echo __('dashboard'); ?></a>
                        <a href="logout.php" class="btn btn-secondary"><?php echo __('logout'); ?></a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline"><?php echo __('login'); ?></a>
                        <a href="register.php" class="btn btn-primary"><?php echo __('register'); ?></a>
                    <?php endif; ?>

                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <select id="language-select" class="language-select" onchange="changeLanguage(this.value)">
                            <?php foreach (getAvailableLanguages() as $code => $name): ?>
                                <option value="<?php echo $code; ?>" <?php echo getCurrentLanguage() === $code ? 'selected' : ''; ?>>
                                    <?php echo strtoupper($code); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <button class="navbar-toggle" id="navbar-toggle" aria-label="Toggle navigation">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-gradient"></div>
            <div class="hero-pattern"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <?php echo __('welcome'); ?>
                        <span class="gradient-text"><?php echo __('site_name'); ?></span>
                        <br><?php echo __('hero_title'); ?>
                    </h1>
                    <p class="hero-description">
                        <?php echo __('hero_description'); ?>
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($stats['total_users'] ?: 1250); ?>+</div>
                            <div class="stat-label"><?php echo __('total_users'); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">$<?php echo number_format($stats['total_deposits'] ?: 850000, 0); ?>+</div>
                            <div class="stat-label"><?php echo __('total_deposits'); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">$<?php echo number_format($stats['total_profits'] ?: 125000, 0); ?>+</div>
                            <div class="stat-label"><?php echo __('total_returns'); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">90+</div>
                            <div class="stat-label"><?php echo __('days_online'); ?></div>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <?php if (!isset($_SESSION['user_id'])): ?>
                            <a href="register.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket"></i>
                                <?php echo __('get_started'); ?>
                            </a>
                            <a href="#plans" class="btn btn-outline btn-lg">
                                <i class="fas fa-info-circle"></i>
                                <?php echo __('learn_more'); ?>
                            </a>
                        <?php else: ?>
                            <a href="dashboard.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-tachometer-alt"></i>
                                <?php echo __('dashboard'); ?>
                            </a>
                            <a href="invest.php" class="btn btn-outline btn-lg">
                                <i class="fas fa-chart-line"></i>
                                <?php echo __('invest_now'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="hero-card">
                        <div class="card-header">
                            <div class="card-title">USDT <?php echo __('balance'); ?></div>
                            <div class="card-balance">$<?php echo number_format(12500.89, 2); ?></div>
                        </div>
                        <div class="card-chart">
                            <canvas id="hero-chart" width="300" height="150"></canvas>
                        </div>
                        <div class="card-stats">
                            <div class="card-stat">
                                <span class="stat-label">24h Change</span>
                                <span class="stat-value positive">+2.45%</span>
                            </div>
                            <div class="card-stat">
                                <span class="stat-label">APY</span>
                                <span class="stat-value">12.5%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Investment Plans Section -->
    <section id="plans" class="plans-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title"><?php echo __('investment_plans'); ?></h2>
                <p class="section-description">
                    <?php echo __('plans_subtitle'); ?>
                </p>
            </div>

            <div class="plans-grid">
                <?php if (empty($plans)): ?>
                    <!-- Default plans if none in database -->
                    <div class="plan-card">
                        <div class="plan-header">
                            <h3 class="plan-name"><?php echo __('plan_starter'); ?></h3>
                            <div class="plan-profit">1.5%</div>
                            <div class="plan-profit-label"><?php echo __('daily_return'); ?></div>
                        </div>
                        <div class="plan-features">
                            <div class="feature">
                                <i class="fas fa-dollar-sign"></i>
                                <span>$50 - $999 USDT</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-calendar"></i>
                                <span>30 <?php echo __('days'); ?></span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-chart-line"></i>
                                <span>45% <?php echo __('total_return'); ?></span>
                            </div>
                        </div>
                        <div class="plan-action">
                            <a href="<?php echo isset($_SESSION['user_id']) ? 'invest.php' : 'register.php'; ?>" class="btn btn-primary w-full">
                                <?php echo isset($_SESSION['user_id']) ? __('invest_now') : __('join_now'); ?>
                            </a>
                        </div>
                    </div>

                    <div class="plan-card featured">
                        <div class="plan-badge">Most Popular</div>
                        <div class="plan-header">
                            <h3 class="plan-name"><?php echo __('plan_advanced'); ?></h3>
                            <div class="plan-profit">2.0%</div>
                            <div class="plan-profit-label"><?php echo __('daily_return'); ?></div>
                        </div>
                        <div class="plan-features">
                            <div class="feature">
                                <i class="fas fa-dollar-sign"></i>
                                <span>$1,000 - $4,999 USDT</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-calendar"></i>
                                <span>45 <?php echo __('days'); ?></span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-chart-line"></i>
                                <span>90% <?php echo __('total_return'); ?></span>
                            </div>
                        </div>
                        <div class="plan-action">
                            <a href="<?php echo isset($_SESSION['user_id']) ? 'invest.php' : 'register.php'; ?>" class="btn btn-primary w-full">
                                <?php echo isset($_SESSION['user_id']) ? __('invest_now') : __('join_now'); ?>
                            </a>
                        </div>
                    </div>

                    <div class="plan-card">
                        <div class="plan-header">
                            <h3 class="plan-name"><?php echo __('plan_professional'); ?></h3>
                            <div class="plan-profit">2.5%</div>
                            <div class="plan-profit-label"><?php echo __('daily_return'); ?></div>
                        </div>
                        <div class="plan-features">
                            <div class="feature">
                                <i class="fas fa-dollar-sign"></i>
                                <span>$5,000 - $19,999 USDT</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-calendar"></i>
                                <span>60 <?php echo __('days'); ?></span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-chart-line"></i>
                                <span>150% <?php echo __('total_return'); ?></span>
                            </div>
                        </div>
                        <div class="plan-action">
                            <a href="<?php echo isset($_SESSION['user_id']) ? 'invest.php' : 'register.php'; ?>" class="btn btn-primary w-full">
                                <?php echo isset($_SESSION['user_id']) ? __('invest_now') : __('join_now'); ?>
                            </a>
                        </div>
                    </div>

                    <div class="plan-card">
                        <div class="plan-header">
                            <h3 class="plan-name"><?php echo __('plan_vip'); ?></h3>
                            <div class="plan-profit">3.0%</div>
                            <div class="plan-profit-label"><?php echo __('daily_return'); ?></div>
                        </div>
                        <div class="plan-features">
                            <div class="feature">
                                <i class="fas fa-dollar-sign"></i>
                                <span>$20,000+ USDT</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-calendar"></i>
                                <span>90 <?php echo __('days'); ?></span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-chart-line"></i>
                                <span>270% <?php echo __('total_return'); ?></span>
                            </div>
                        </div>
                        <div class="plan-action">
                            <a href="<?php echo isset($_SESSION['user_id']) ? 'invest.php' : 'register.php'; ?>" class="btn btn-primary w-full">
                                <?php echo isset($_SESSION['user_id']) ? __('invest_now') : __('join_now'); ?>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($plans as $index => $plan): ?>
                    <div class="plan-card <?php echo $index === 1 ? 'featured' : ''; ?>">
                        <?php if ($index === 1): ?>
                            <div class="plan-badge">Most Popular</div>
                        <?php endif; ?>

                        <div class="plan-header">
                            <h3 class="plan-name"><?php echo htmlspecialchars($plan['name']); ?></h3>
                            <div class="plan-profit"><?php echo $plan['daily_profit']; ?>%</div>
                            <div class="plan-profit-label"><?php echo __('daily_return'); ?></div>
                        </div>

                        <div class="plan-features">
                            <div class="feature">
                                <i class="fas fa-dollar-sign"></i>
                                <span>$<?php echo number_format($plan['min_amount'], 0); ?> - $<?php echo number_format($plan['max_amount'], 0); ?> USDT</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo $plan['duration_days']; ?> <?php echo __('days'); ?></span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-chart-line"></i>
                                <span><?php echo $plan['total_return']; ?>% <?php echo __('total_return'); ?></span>
                            </div>
                        </div>

                        <?php if (!empty($plan['description'])): ?>
                        <div class="plan-description">
                            <?php echo htmlspecialchars($plan['description']); ?>
                        </div>
                        <?php endif; ?>

                        <div class="plan-action">
                            <?php if (isset($_SESSION['user_id'])): ?>
                                <a href="invest.php?plan=<?php echo $plan['id']; ?>" class="btn btn-primary w-full">
                                    <?php echo __('invest_now'); ?>
                                </a>
                            <?php else: ?>
                                <a href="register.php" class="btn btn-primary w-full">
                                    <?php echo __('join_now'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title"><?php echo __('why_choose_us'); ?></h2>
                <p class="section-description">
                    <?php echo __('why_choose_subtitle'); ?>
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title"><?php echo __('feature_security'); ?></h3>
                    <p class="feature-description">
                        <?php echo __('feature_security_desc'); ?>
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title"><?php echo __('feature_returns'); ?></h3>
                    <p class="feature-description">
                        <?php echo __('feature_returns_desc'); ?>
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title"><?php echo __('feature_referrals'); ?></h3>
                    <p class="feature-description">
                        <?php echo __('feature_referrals_desc'); ?>
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title"><?php echo __('feature_mobile'); ?></h3>
                    <p class="feature-description">
                        <?php echo __('feature_mobile_desc'); ?>
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title"><?php echo __('feature_support'); ?></h3>
                    <p class="feature-description">
                        <?php echo __('feature_support_desc'); ?>
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title"><?php echo __('feature_instant'); ?></h3>
                    <p class="feature-description">
                        <?php echo __('feature_instant_desc'); ?>
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="assets/images/logo.svg" alt="<?php echo __('site_name'); ?>" class="footer-logo">
                        <p class="footer-description">
                            <?php echo __('footer_about_text'); ?>
                        </p>
                    </div>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title"><?php echo __('quick_links'); ?></h4>
                    <ul class="footer-links">
                        <li><a href="#home"><?php echo __('home'); ?></a></li>
                        <li><a href="#plans"><?php echo __('investment_plans'); ?></a></li>
                        <li><a href="#features"><?php echo __('about'); ?></a></li>
                        <li><a href="<?php echo isset($_SESSION['user_id']) ? 'dashboard.php' : 'register.php'; ?>">
                            <?php echo isset($_SESSION['user_id']) ? __('dashboard') : __('register'); ?>
                        </a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title"><?php echo __('legal'); ?></h4>
                    <ul class="footer-links">
                        <li><a href="terms.php"><?php echo __('terms_of_service'); ?></a></li>
                        <li><a href="privacy.php"><?php echo __('privacy_policy'); ?></a></li>
                        <li><a href="support.php"><?php echo __('support'); ?></a></li>
                        <li><a href="#contact"><?php echo __('contact_us'); ?></a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 class="footer-title"><?php echo __('contact_info'); ?></h4>
                    <div class="footer-contact">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-globe"></i>
                            <span>www.astrogenix.com</span>
                        </div>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-telegram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-discord"></i></a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 <?php echo __('site_name'); ?>. <?php echo __('all_rights_reserved'); ?></p>
                </div>
                <div class="footer-language">
                    <select id="footer-language-select" class="language-select" onchange="changeLanguage(this.value)">
                        <?php foreach (getAvailableLanguages() as $code => $name): ?>
                            <option value="<?php echo $code; ?>" <?php echo getCurrentLanguage() === $code ? 'selected' : ''; ?>>
                                <?php echo $name; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js"></script>
    <script>
        // Language switcher
        function changeLanguage(lang) {
            window.location.href = '?lang=' + lang;
        }

        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const navbarToggle = document.getElementById('navbar-toggle');
            const navbarMenu = document.getElementById('navbar-menu');

            if (navbarToggle && navbarMenu) {
                navbarToggle.addEventListener('click', function() {
                    navbarMenu.classList.toggle('active');
                    navbarToggle.classList.toggle('active');
                });
            }

            // Hero chart
            const heroChart = document.getElementById('hero-chart');
            if (heroChart) {
                const ctx = heroChart.getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Portfolio Value',
                            data: [8500, 9200, 10100, 11200, 11800, 12500],
                            borderColor: '#f0b90b',
                            backgroundColor: 'rgba(240, 185, 11, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                display: false
                            }
                        },
                        elements: {
                            point: {
                                radius: 0
                            }
                        }
                    }
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
