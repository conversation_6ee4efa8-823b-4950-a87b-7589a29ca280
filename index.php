<?php
/**
 * AstroGenix - Main Page
 * Главная страница платформы стейкинга USDT
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Получение статистики для главной страницы
try {
    $stats_query = "
        SELECT 
            (SELECT COUNT(*) FROM users WHERE status = 'active') as total_users,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'deposit' AND status = 'approved') as total_deposits,
            (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'withdrawal' AND status = 'approved') as total_withdrawals,
            (SELECT COALESCE(SUM(total_earned), 0) FROM users) as total_profits
    ";
    $stats = $pdo->query($stats_query)->fetch();
    
    // Получение активных инвестиционных планов
    $plans_query = "SELECT * FROM investment_plans WHERE status = 'active' ORDER BY sort_order ASC";
    $plans = $pdo->query($plans_query)->fetchAll();
    
    // Получение последних новостей
    $news_query = "SELECT * FROM news WHERE status = 'published' ORDER BY published_at DESC LIMIT 3";
    $latest_news = $pdo->query($news_query)->fetchAll();
    
} catch (PDOException $e) {
    $stats = ['total_users' => 0, 'total_deposits' => 0, 'total_withdrawals' => 0, 'total_profits' => 0];
    $plans = [];
    $latest_news = [];
}

$page_title = __('welcome') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    <meta name="description" content="<?php echo e(getSetting('site_description')); ?>">
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="navbar-brand">
                    <a href="<?php echo SITE_URL; ?>">
                        <img src="assets/images/logo.png" alt="<?php echo e(getSetting('site_name')); ?>" class="logo">
                        <span class="brand-text"><?php echo e(getSetting('site_name')); ?></span>
                    </a>
                </div>
                
                <div class="navbar-menu" id="navbar-menu">
                    <ul class="navbar-nav">
                        <li><a href="#home" class="nav-link"><?php echo __('home'); ?></a></li>
                        <li><a href="#plans" class="nav-link"><?php echo __('investment_plans'); ?></a></li>
                        <li><a href="#about" class="nav-link"><?php echo __('about'); ?></a></li>
                        <li><a href="#news" class="nav-link"><?php echo __('news'); ?></a></li>
                        <li><a href="#contact" class="nav-link"><?php echo __('contact'); ?></a></li>
                    </ul>
                </div>
                
                <div class="navbar-actions">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="dashboard.php" class="btn btn-primary"><?php echo __('dashboard'); ?></a>
                        <a href="logout.php" class="btn btn-outline"><?php echo __('logout'); ?></a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline"><?php echo __('login'); ?></a>
                        <a href="register.php" class="btn btn-primary"><?php echo __('register'); ?></a>
                    <?php endif; ?>
                    
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <select id="language-select" onchange="changeLanguage(this.value)">
                            <option value="en" <?php echo $language === 'en' ? 'selected' : ''; ?>>EN</option>
                            <option value="ru" <?php echo $language === 'ru' ? 'selected' : ''; ?>>RU</option>
                        </select>
                    </div>
                </div>
                
                <div class="navbar-toggle" id="navbar-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-particles"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <?php echo __('welcome'); ?> <?php echo __('site_name'); ?>
                        <span class="gradient-text">USDT Staking Platform</span>
                    </h1>
                    <p class="hero-description">
                        <?php echo e(getSetting('site_description')); ?>. 
                        Начните зарабатывать с минимальными рисками и максимальной прибылью.
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($stats['total_users']); ?>+</div>
                            <div class="stat-label"><?php echo __('total_users'); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">$<?php echo number_format($stats['total_deposits'], 0); ?>+</div>
                            <div class="stat-label"><?php echo __('total_deposits'); ?></div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">$<?php echo number_format($stats['total_profits'], 0); ?>+</div>
                            <div class="stat-label"><?php echo __('total_profits'); ?></div>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <?php if (!isset($_SESSION['user_id'])): ?>
                            <a href="register.php" class="btn btn-primary btn-lg"><?php echo __('get_started'); ?></a>
                            <a href="#plans" class="btn btn-outline btn-lg"><?php echo __('learn_more'); ?></a>
                        <?php else: ?>
                            <a href="dashboard.php" class="btn btn-primary btn-lg"><?php echo __('dashboard'); ?></a>
                            <a href="invest.php" class="btn btn-outline btn-lg"><?php echo __('invest_now'); ?></a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="hero-card">
                        <div class="card-header">
                            <div class="card-title">USDT Staking</div>
                            <div class="card-balance">$<?php echo number_format(12500.89, 2); ?></div>
                        </div>
                        <div class="card-chart">
                            <canvas id="hero-chart"></canvas>
                        </div>
                        <div class="card-stats">
                            <div class="card-stat">
                                <span class="stat-label">24h Change</span>
                                <span class="stat-value positive">+2.45%</span>
                            </div>
                            <div class="card-stat">
                                <span class="stat-label">APY</span>
                                <span class="stat-value">12.5%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Investment Plans Section -->
    <section id="plans" class="plans-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title"><?php echo __('investment_plans'); ?></h2>
                <p class="section-description">
                    Выберите подходящий план инвестирования и начните получать стабильную прибыль
                </p>
            </div>
            
            <div class="plans-grid">
                <?php foreach ($plans as $index => $plan): ?>
                <div class="plan-card <?php echo $index === 1 ? 'featured' : ''; ?>">
                    <?php if ($index === 1): ?>
                        <div class="plan-badge">Most Popular</div>
                    <?php endif; ?>
                    
                    <div class="plan-header">
                        <h3 class="plan-name"><?php echo e($plan['name']); ?></h3>
                        <div class="plan-profit"><?php echo $plan['daily_profit']; ?>%</div>
                        <div class="plan-profit-label"><?php echo __('daily_profit'); ?></div>
                    </div>
                    
                    <div class="plan-features">
                        <div class="feature">
                            <i class="fas fa-dollar-sign"></i>
                            <span><?php echo formatAmount($plan['min_amount'], 0); ?> - <?php echo formatAmount($plan['max_amount'], 0); ?> USDT</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo $plan['duration_days']; ?> <?php echo __('days'); ?></span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-chart-line"></i>
                            <span><?php echo $plan['total_return']; ?>% <?php echo __('total_return'); ?></span>
                        </div>
                    </div>
                    
                    <div class="plan-description">
                        <?php echo e($plan['description']); ?>
                    </div>
                    
                    <div class="plan-action">
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <a href="invest.php?plan=<?php echo $plan['id']; ?>" class="btn btn-primary btn-block">
                                <?php echo __('invest_now'); ?>
                            </a>
                        <?php else: ?>
                            <a href="register.php" class="btn btn-primary btn-block">
                                <?php echo __('join_now'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Почему выбирают AstroGenix?</h2>
                <p class="section-description">
                    Мы предлагаем лучшие условия для стейкинга USDT с максимальной безопасностью
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">Безопасность</h3>
                    <p class="feature-description">
                        Многоуровневая система безопасности и защита средств пользователей
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Высокая доходность</h3>
                    <p class="feature-description">
                        Стабильная ежедневная прибыль до 3% с различными инвестиционными планами
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title">Реферальная программа</h3>
                    <p class="feature-description">
                        Многоуровневая реферальная система с комиссией до 10%
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Мобильная версия</h3>
                    <p class="feature-description">
                        Полнофункциональная мобильная версия для управления инвестициями
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">Поддержка 24/7</h3>
                    <p class="feature-description">
                        Круглосуточная техническая поддержка на русском и английском языках
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">Быстрые выплаты</h3>
                    <p class="feature-description">
                        Мгновенное начисление прибыли и быстрая обработка заявок на вывод
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
