<?php
/**
 * AstroGenix - Sidebar Component
 * Боковая панель навигации для пользователей
 */

if (!defined('ASTROGENIX_INIT')) {
    die('Direct access not allowed');
}

$current_page = basename($_SERVER['PHP_SELF']);
$user = getCurrentUser();
?>

<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <img src="assets/images/logo.png" alt="<?php echo e(getSetting('site_name')); ?>">
            <span><?php echo e(getSetting('site_name')); ?></span>
        </div>
        <button class="sidebar-toggle" id="sidebar-toggle">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <nav class="sidebar-nav">
        <ul class="nav-menu">
            <li class="nav-item <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span><?php echo __('dashboard'); ?></span>
                </a>
            </li>
            
            <li class="nav-item <?php echo in_array($current_page, ['invest.php', 'investments.php']) ? 'active' : ''; ?>">
                <a href="invest.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    <span><?php echo __('investments'); ?></span>
                </a>
            </li>
            
            <li class="nav-item <?php echo in_array($current_page, ['deposit.php', 'withdraw.php', 'transactions.php']) ? 'active' : ''; ?>">
                <a href="transactions.php" class="nav-link">
                    <i class="fas fa-exchange-alt"></i>
                    <span><?php echo __('transactions'); ?></span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $current_page === 'referrals.php' ? 'active' : ''; ?>">
                <a href="referrals.php" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span><?php echo __('referrals'); ?></span>
                    <?php if ($user && $user['total_referrals'] > 0): ?>
                        <span class="nav-badge"><?php echo $user['total_referrals']; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item <?php echo $current_page === 'tasks.php' ? 'active' : ''; ?>">
                <a href="tasks.php" class="nav-link">
                    <i class="fas fa-tasks"></i>
                    <span><?php echo __('tasks'); ?></span>
                    <?php
                    // Получаем количество доступных задач
                    try {
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) 
                            FROM tasks t 
                            LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
                            WHERE t.status = 'active' AND (ut.status IS NULL OR ut.status = 'completed')
                        ");
                        $stmt->execute([$user['id']]);
                        $available_tasks = $stmt->fetchColumn();
                        
                        if ($available_tasks > 0):
                    ?>
                        <span class="nav-badge"><?php echo $available_tasks; ?></span>
                    <?php 
                        endif;
                    } catch (PDOException $e) {
                        // Игнорируем ошибки
                    }
                    ?>
                </a>
            </li>
            
            <li class="nav-item <?php echo $current_page === 'support.php' ? 'active' : ''; ?>">
                <a href="support.php" class="nav-link">
                    <i class="fas fa-headset"></i>
                    <span><?php echo __('support'); ?></span>
                </a>
            </li>
            
            <li class="nav-item <?php echo $current_page === 'profile.php' ? 'active' : ''; ?>">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span><?php echo __('profile'); ?></span>
                </a>
            </li>
            
            <!-- Дополнительные ссылки -->
            <li class="nav-divider"></li>
            
            <li class="nav-item">
                <a href="<?php echo SITE_URL; ?>" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Главная</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a href="news.php" class="nav-link">
                    <i class="fas fa-newspaper"></i>
                    <span><?php echo __('news'); ?></span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <div class="user-info">
            <div class="user-avatar">
                <?php if (!empty($user['first_name'])): ?>
                    <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
            </div>
            <div class="user-details">
                <div class="user-name">
                    <?php echo e($user['first_name'] ? $user['first_name'] . ' ' . $user['last_name'] : $user['username']); ?>
                </div>
                <div class="user-email"><?php echo e($user['email']); ?></div>
                <div class="user-balance">
                    <i class="fas fa-wallet"></i>
                    $<?php echo formatAmount($user['balance'], 2); ?>
                </div>
            </div>
        </div>
        
        <!-- Быстрые действия -->
        <div class="quick-actions-sidebar">
            <a href="deposit.php" class="quick-action-btn deposit">
                <i class="fas fa-plus"></i>
                <span>Пополнить</span>
            </a>
            <a href="withdraw.php" class="quick-action-btn withdraw">
                <i class="fas fa-minus"></i>
                <span>Вывести</span>
            </a>
        </div>
        
        <a href="logout.php" class="logout-btn" onclick="return confirm('Вы уверены, что хотите выйти?')">
            <i class="fas fa-sign-out-alt"></i>
            <?php echo __('logout'); ?>
        </a>
    </div>
</aside>

<style>
/* Дополнительные стили для сайдбара */
.nav-badge {
    background: var(--primary-color);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    margin-left: auto;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.nav-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-md) var(--spacing-lg);
}

.user-balance {
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.quick-actions-sidebar {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.quick-action-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: var(--bg-input);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-xs);
    transition: var(--transition-fast);
}

.quick-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.quick-action-btn.deposit:hover {
    border-color: var(--success-color);
    color: var(--success-color);
}

.quick-action-btn.withdraw:hover {
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.quick-action-btn i {
    font-size: var(--font-size-sm);
}

/* Адаптивность */
@media (max-width: 1023px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
}

/* Анимации */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.1), transparent);
    transition: var(--transition-base);
}

.nav-link:hover::before {
    left: 100%;
}

/* Статус онлайн */
.user-avatar {
    position: relative;
}

.user-avatar::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--success-color);
    border: 2px solid var(--bg-secondary);
    border-radius: 50%;
}

/* Уведомления в навигации */
.nav-item.has-notification .nav-link::after {
    content: '';
    position: absolute;
    top: 50%;
    right: var(--spacing-lg);
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: var(--danger-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(246, 70, 93, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(246, 70, 93, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(246, 70, 93, 0);
    }
}
</style>
