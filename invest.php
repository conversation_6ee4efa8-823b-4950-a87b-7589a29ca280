<?php
/**
 * AstroGenix - Investment Page
 * Страница создания инвестиций
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

$error = '';
$success = '';
$selected_plan_id = $_GET['plan'] ?? null;

// Получение активных планов
try {
    $stmt = $pdo->query("SELECT * FROM investment_plans WHERE status = 'active' ORDER BY sort_order ASC");
    $plans = $stmt->fetchAll();
} catch (PDOException $e) {
    $plans = [];
}

// Обработка создания инвестиции
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception(__('csrf_token_mismatch'));
        }
        
        $plan_id = (int)($_POST['plan_id'] ?? 0);
        $amount = (float)($_POST['amount'] ?? 0);
        
        if ($plan_id <= 0 || $amount <= 0) {
            throw new Exception('Выберите план и укажите сумму инвестиции');
        }
        
        createInvestment($user['id'], $plan_id, $amount);
        
        $success = 'Инвестиция создана успешно!';
        
        // Обновляем данные пользователя
        $user = getCurrentUser();
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

$page_title = __('investments') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('invest_now'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
            </div>
        </header>
        
        <!-- Investment Content -->
        <div class="dashboard-content">
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo e($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo e($success); ?>
                </div>
            <?php endif; ?>
            
            <!-- Investment Plans -->
            <div class="plans-section">
                <div class="section-header">
                    <h2>Выберите инвестиционный план</h2>
                    <p>Инвестируйте в USDT и получайте стабильную ежедневную прибыль</p>
                </div>
                
                <div class="plans-grid">
                    <?php foreach ($plans as $index => $plan): ?>
                        <div class="plan-card <?php echo $index === 1 ? 'featured' : ''; ?> <?php echo $selected_plan_id == $plan['id'] ? 'selected' : ''; ?>" 
                             data-plan-id="<?php echo $plan['id']; ?>"
                             onclick="selectPlan(<?php echo $plan['id']; ?>)">
                            
                            <?php if ($index === 1): ?>
                                <div class="plan-badge">Популярный</div>
                            <?php endif; ?>
                            
                            <div class="plan-header">
                                <h3 class="plan-name"><?php echo e($plan['name']); ?></h3>
                                <div class="plan-profit"><?php echo $plan['daily_profit']; ?>%</div>
                                <div class="plan-profit-label"><?php echo __('daily_profit'); ?></div>
                            </div>
                            
                            <div class="plan-features">
                                <div class="feature">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$<?php echo formatAmount($plan['min_amount'], 0); ?> - $<?php echo formatAmount($plan['max_amount'], 0); ?></span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-calendar"></i>
                                    <span><?php echo $plan['duration_days']; ?> <?php echo __('days'); ?></span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-chart-line"></i>
                                    <span><?php echo $plan['total_return']; ?>% <?php echo __('total_return'); ?></span>
                                </div>
                            </div>
                            
                            <div class="plan-description">
                                <?php echo e($plan['description']); ?>
                            </div>
                            
                            <div class="plan-calculator">
                                <div class="calc-row">
                                    <span>Инвестиция $100:</span>
                                    <span class="calc-value">+$<?php echo number_format(100 * ($plan['daily_profit'] / 100), 2); ?>/день</span>
                                </div>
                                <div class="calc-row">
                                    <span>За <?php echo $plan['duration_days']; ?> дней:</span>
                                    <span class="calc-value profit">+$<?php echo number_format(100 * ($plan['total_return'] / 100), 2); ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Investment Form -->
            <div class="investment-form-section">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Создать инвестицию</h3>
                    </div>
                    <div class="card-content">
                        <form method="POST" class="investment-form" id="investment-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="plan_id" id="selected-plan-id" value="<?php echo $selected_plan_id; ?>">
                            
                            <div class="form-group">
                                <label for="amount">Сумма инвестиции (USDT)</label>
                                <div class="amount-input">
                                    <input type="number" id="amount" name="amount" step="0.01" min="1" 
                                           placeholder="Введите сумму" required>
                                    <div class="amount-buttons">
                                        <button type="button" class="amount-btn" onclick="setAmount(100)">$100</button>
                                        <button type="button" class="amount-btn" onclick="setAmount(500)">$500</button>
                                        <button type="button" class="amount-btn" onclick="setAmount(1000)">$1000</button>
                                        <button type="button" class="amount-btn" onclick="setMaxAmount()">MAX</button>
                                    </div>
                                </div>
                                <div class="amount-info">
                                    <span>Доступно: $<?php echo formatAmount($user['balance'], 2); ?></span>
                                </div>
                            </div>
                            
                            <div class="investment-preview" id="investment-preview" style="display: none;">
                                <div class="preview-header">
                                    <h4>Предварительный расчет</h4>
                                </div>
                                <div class="preview-content">
                                    <div class="preview-row">
                                        <span>План:</span>
                                        <span id="preview-plan">-</span>
                                    </div>
                                    <div class="preview-row">
                                        <span>Сумма инвестиции:</span>
                                        <span id="preview-amount">$0.00</span>
                                    </div>
                                    <div class="preview-row">
                                        <span>Ежедневная прибыль:</span>
                                        <span id="preview-daily" class="profit">+$0.00</span>
                                    </div>
                                    <div class="preview-row">
                                        <span>Общая прибыль:</span>
                                        <span id="preview-total" class="profit">+$0.00</span>
                                    </div>
                                    <div class="preview-row">
                                        <span>Длительность:</span>
                                        <span id="preview-duration">0 дней</span>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-block btn-lg" id="invest-btn" disabled>
                                <i class="fas fa-chart-line"></i>
                                Создать инвестицию
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>
    
    <script>
        const plans = <?php echo json_encode($plans); ?>;
        const userBalance = <?php echo $user['balance']; ?>;
        let selectedPlan = null;
        
        // Выбор плана
        function selectPlan(planId) {
            // Убираем выделение с других планов
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Выделяем выбранный план
            const selectedCard = document.querySelector(`[data-plan-id="${planId}"]`);
            selectedCard.classList.add('selected');
            
            // Находим план в данных
            selectedPlan = plans.find(plan => plan.id == planId);
            
            // Обновляем форму
            document.getElementById('selected-plan-id').value = planId;
            document.getElementById('invest-btn').disabled = false;
            
            // Обновляем лимиты суммы
            const amountInput = document.getElementById('amount');
            amountInput.min = selectedPlan.min_amount;
            amountInput.max = Math.min(selectedPlan.max_amount, userBalance);
            
            // Обновляем предварительный расчет
            updatePreview();
        }
        
        // Установка суммы
        function setAmount(amount) {
            document.getElementById('amount').value = amount;
            updatePreview();
        }
        
        function setMaxAmount() {
            if (selectedPlan) {
                const maxAmount = Math.min(selectedPlan.max_amount, userBalance);
                document.getElementById('amount').value = maxAmount;
                updatePreview();
            }
        }
        
        // Обновление предварительного расчета
        function updatePreview() {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const preview = document.getElementById('investment-preview');
            
            if (selectedPlan && amount > 0) {
                const dailyProfit = amount * (selectedPlan.daily_profit / 100);
                const totalProfit = amount * (selectedPlan.total_return / 100);
                
                document.getElementById('preview-plan').textContent = selectedPlan.name;
                document.getElementById('preview-amount').textContent = '$' + amount.toFixed(2);
                document.getElementById('preview-daily').textContent = '+$' + dailyProfit.toFixed(2);
                document.getElementById('preview-total').textContent = '+$' + totalProfit.toFixed(2);
                document.getElementById('preview-duration').textContent = selectedPlan.duration_days + ' дней';
                
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        }
        
        // Обработчики событий
        document.getElementById('amount').addEventListener('input', updatePreview);
        
        // Автовыбор плана, если передан в URL
        <?php if ($selected_plan_id): ?>
            selectPlan(<?php echo $selected_plan_id; ?>);
        <?php endif; ?>
        
        // Валидация формы
        document.getElementById('investment-form').addEventListener('submit', function(e) {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            
            if (!selectedPlan) {
                e.preventDefault();
                showNotification('Выберите инвестиционный план', 'error');
                return;
            }
            
            if (amount < selectedPlan.min_amount) {
                e.preventDefault();
                showNotification(`Минимальная сумма: $${selectedPlan.min_amount}`, 'error');
                return;
            }
            
            if (amount > selectedPlan.max_amount) {
                e.preventDefault();
                showNotification(`Максимальная сумма: $${selectedPlan.max_amount}`, 'error');
                return;
            }
            
            if (amount > userBalance) {
                e.preventDefault();
                showNotification('Недостаточно средств на балансе', 'error');
                return;
            }
        });
    </script>
</body>
</html>
