<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0b90b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d9a441;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0b90b;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background circle for the icon -->
  <circle cx="30" cy="30" r="25" fill="rgba(240, 185, 11, 0.1)" stroke="url(#logoGradient)" stroke-width="2"/>

  <!-- Star/Astro symbol -->
  <path d="M30 10 L32.5 22.5 L45 20 L35 27.5 L42.5 37.5 L30 32.5 L17.5 37.5 L25 27.5 L15 20 L27.5 22.5 Z"
        fill="url(#logoGradient)" stroke="#f0b90b" stroke-width="1"/>

  <!-- Central dot -->
  <circle cx="30" cy="30" r="3" fill="#ffffff"/>

  <!-- Text "AstroGenix" -->
  <text x="70" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#textGradient)">
    Astro
  </text>
  <text x="70" y="45" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#f0b90b">
    Genix
  </text>
</svg>
