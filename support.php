<?php
/**
 * AstroGenix - Support Page
 * Страница службы поддержки
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

$error = '';
$success = '';

// Обработка создания нового тикета
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_ticket'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');
        $priority = $_POST['priority'] ?? 'medium';
        $category = $_POST['category'] ?? 'general';
        
        // Валидация
        if (empty($subject)) {
            $error = 'Укажите тему обращения';
        } elseif (strlen($subject) < 5) {
            $error = 'Тема должна содержать минимум 5 символов';
        } elseif (empty($message)) {
            $error = 'Опишите вашу проблему';
        } elseif (strlen($message) < 10) {
            $error = 'Сообщение должно содержать минимум 10 символов';
        } else {
            try {
                $pdo->beginTransaction();
                
                // Создание тикета
                $ticket_stmt = $pdo->prepare("
                    INSERT INTO support_tickets (user_id, subject, category, priority, status, created_at) 
                    VALUES (?, ?, ?, ?, 'open', NOW())
                ");
                $ticket_stmt->execute([$user['id'], $subject, $category, $priority]);
                $ticket_id = $pdo->lastInsertId();
                
                // Создание первого сообщения
                $message_stmt = $pdo->prepare("
                    INSERT INTO ticket_messages (ticket_id, user_id, message, is_admin, created_at) 
                    VALUES (?, ?, ?, 0, NOW())
                ");
                $message_stmt->execute([$ticket_id, $user['id'], $message]);
                
                // Логирование
                logActivity($user['id'], 'support_ticket_created', "Создан тикет #{$ticket_id}: {$subject}");
                
                $pdo->commit();
                
                $success = "Тикет #{$ticket_id} успешно создан! Мы ответим вам в ближайшее время.";
                
                // Очищаем форму
                $_POST = [];
                
            } catch (PDOException $e) {
                $pdo->rollBack();
                $error = 'Ошибка создания тикета';
                error_log("Support ticket error: " . $e->getMessage());
            }
        }
    }
}

// Обработка отправки сообщения в тикет
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Ошибка безопасности. Попробуйте еще раз.';
    } else {
        $ticket_id = intval($_POST['ticket_id'] ?? 0);
        $message = trim($_POST['message'] ?? '');
        
        if (empty($message)) {
            $error = 'Введите сообщение';
        } elseif (strlen($message) < 5) {
            $error = 'Сообщение должно содержать минимум 5 символов';
        } else {
            try {
                // Проверяем, что тикет принадлежит пользователю
                $ticket_check_stmt = $pdo->prepare("
                    SELECT id FROM support_tickets 
                    WHERE id = ? AND user_id = ? AND status != 'closed'
                ");
                $ticket_check_stmt->execute([$ticket_id, $user['id']]);
                
                if (!$ticket_check_stmt->fetch()) {
                    $error = 'Тикет не найден или закрыт';
                } else {
                    // Добавляем сообщение
                    $message_stmt = $pdo->prepare("
                        INSERT INTO ticket_messages (ticket_id, user_id, message, is_admin, created_at) 
                        VALUES (?, ?, ?, 0, NOW())
                    ");
                    $message_stmt->execute([$ticket_id, $user['id'], $message]);
                    
                    // Обновляем статус тикета
                    $update_ticket_stmt = $pdo->prepare("
                        UPDATE support_tickets 
                        SET status = 'awaiting_admin', updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $update_ticket_stmt->execute([$ticket_id]);
                    
                    $success = 'Сообщение отправлено!';
                }
            } catch (PDOException $e) {
                $error = 'Ошибка отправки сообщения';
                error_log("Support message error: " . $e->getMessage());
            }
        }
    }
}

// Получение тикетов пользователя
try {
    $tickets_stmt = $pdo->prepare("
        SELECT st.*, 
               COUNT(tm.id) as messages_count,
               MAX(tm.created_at) as last_message_at
        FROM support_tickets st 
        LEFT JOIN ticket_messages tm ON st.id = tm.ticket_id
        WHERE st.user_id = ? 
        GROUP BY st.id
        ORDER BY st.updated_at DESC, st.created_at DESC
        LIMIT 20
    ");
    $tickets_stmt->execute([$user['id']]);
    $tickets = $tickets_stmt->fetchAll();
    
    // Статистика тикетов
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_tickets,
            COUNT(CASE WHEN status = 'open' THEN 1 END) as open_tickets,
            COUNT(CASE WHEN status = 'awaiting_admin' THEN 1 END) as awaiting_tickets,
            COUNT(CASE WHEN status = 'answered' THEN 1 END) as answered_tickets,
            COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_tickets
        FROM support_tickets 
        WHERE user_id = ?
    ");
    $stats_stmt->execute([$user['id']]);
    $ticket_stats = $stats_stmt->fetch();
    
} catch (PDOException $e) {
    $tickets = [];
    $ticket_stats = [
        'total_tickets' => 0,
        'open_tickets' => 0,
        'awaiting_tickets' => 0,
        'answered_tickets' => 0,
        'closed_tickets' => 0
    ];
}

$page_title = __('support') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('support'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-primary btn-sm" onclick="openModal('create-ticket-modal')">
                        <i class="fas fa-plus"></i>
                        Новый тикет
                    </button>
                </div>
            </div>
        </header>
        
        <!-- Support Content -->
        <div class="dashboard-content">
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo e($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo e($success); ?>
                </div>
            <?php endif; ?>
            
            <!-- Support Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $ticket_stats['total_tickets']; ?></div>
                        <div class="stat-label">Всего тикетов</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock text-warning"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $ticket_stats['open_tickets'] + $ticket_stats['awaiting_tickets']; ?></div>
                        <div class="stat-label">Активных</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-reply text-info"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $ticket_stats['answered_tickets']; ?></div>
                        <div class="stat-label">Отвечено</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check text-success"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $ticket_stats['closed_tickets']; ?></div>
                        <div class="stat-label">Закрыто</div>
                    </div>
                </div>
            </div>

            <!-- Support Tickets -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Мои тикеты</h3>
                </div>
                <div class="card-content">
                    <?php if (empty($tickets)): ?>
                        <div class="empty-state">
                            <i class="fas fa-headset"></i>
                            <h3>У вас нет тикетов</h3>
                            <p>Создайте новый тикет, если у вас есть вопросы или проблемы</p>
                            <button class="btn btn-primary" onclick="openModal('create-ticket-modal')">
                                <i class="fas fa-plus"></i>
                                Создать тикет
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="tickets-list">
                            <?php foreach ($tickets as $ticket): ?>
                                <div class="ticket-item" data-ticket-id="<?php echo $ticket['id']; ?>">
                                    <div class="ticket-header">
                                        <div class="ticket-info">
                                            <h4 class="ticket-subject">
                                                <span class="ticket-id">#<?php echo str_pad($ticket['id'], 4, '0', STR_PAD_LEFT); ?></span>
                                                <?php echo e($ticket['subject']); ?>
                                            </h4>
                                            <div class="ticket-meta">
                                                <span class="ticket-category">
                                                    <i class="fas fa-tag"></i>
                                                    <?php
                                                    switch($ticket['category']) {
                                                        case 'general': echo 'Общие вопросы'; break;
                                                        case 'technical': echo 'Технические проблемы'; break;
                                                        case 'financial': echo 'Финансовые вопросы'; break;
                                                        case 'account': echo 'Аккаунт'; break;
                                                        default: echo ucfirst($ticket['category']);
                                                    }
                                                    ?>
                                                </span>
                                                <span class="ticket-priority priority-<?php echo $ticket['priority']; ?>">
                                                    <i class="fas fa-flag"></i>
                                                    <?php
                                                    switch($ticket['priority']) {
                                                        case 'low': echo 'Низкий'; break;
                                                        case 'medium': echo 'Средний'; break;
                                                        case 'high': echo 'Высокий'; break;
                                                        case 'urgent': echo 'Срочный'; break;
                                                        default: echo ucfirst($ticket['priority']);
                                                    }
                                                    ?>
                                                </span>
                                                <span class="ticket-messages">
                                                    <i class="fas fa-comments"></i>
                                                    <?php echo $ticket['messages_count']; ?> сообщений
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ticket-status">
                                            <span class="status-badge status-<?php echo $ticket['status']; ?>">
                                                <?php
                                                switch($ticket['status']) {
                                                    case 'open': echo 'Открыт'; break;
                                                    case 'awaiting_admin': echo 'Ожидает ответа'; break;
                                                    case 'answered': echo 'Отвечено'; break;
                                                    case 'closed': echo 'Закрыт'; break;
                                                    default: echo ucfirst($ticket['status']);
                                                }
                                                ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="ticket-footer">
                                        <div class="ticket-dates">
                                            <span class="created-date">
                                                Создан: <?php echo formatDate($ticket['created_at'], 'd.m.Y H:i'); ?>
                                            </span>
                                            <?php if ($ticket['last_message_at']): ?>
                                                <span class="last-message-date">
                                                    Последнее сообщение: <?php echo formatDate($ticket['last_message_at'], 'd.m.Y H:i'); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ticket-actions">
                                            <button class="btn btn-outline btn-sm" onclick="viewTicket(<?php echo $ticket['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                                Просмотр
                                            </button>
                                            <?php if ($ticket['status'] !== 'closed'): ?>
                                                <button class="btn btn-primary btn-sm" onclick="replyToTicket(<?php echo $ticket['id']; ?>)">
                                                    <i class="fas fa-reply"></i>
                                                    Ответить
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Часто задаваемые вопросы</h3>
                </div>
                <div class="card-content">
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Как пополнить баланс?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Перейдите в раздел "Пополнение", выберите способ оплаты, укажите сумму и следуйте инструкциям. Средства поступят на баланс после подтверждения транзакции.</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Как вывести средства?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>В разделе "Вывод средств" укажите сумму, выберите способ вывода, введите адрес кошелька и подтвердите операцию паролем. Обработка занимает 24-48 часов.</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Как работает реферальная программа?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>Приглашайте друзей по вашей реферальной ссылке и получайте комиссию с их инвестиций: 10% с первого уровня, 5% со второго и 2% с третьего.</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <h4>Какие есть инвестиционные планы?</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>У нас есть 4 плана: Starter (1% в день, 30 дней), Advanced (1.5% в день, 45 дней), Professional (2% в день, 60 дней) и VIP (2.5% в день, 90 дней).</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Create Ticket Modal -->
    <div id="create-ticket-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Создать новый тикет</h3>
                <button class="modal-close" onclick="closeModal('create-ticket-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="POST" class="ticket-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="form-group">
                        <label for="category">Категория</label>
                        <select name="category" id="category" required>
                            <option value="general">Общие вопросы</option>
                            <option value="technical">Технические проблемы</option>
                            <option value="financial">Финансовые вопросы</option>
                            <option value="account">Аккаунт</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="priority">Приоритет</label>
                        <select name="priority" id="priority" required>
                            <option value="low">Низкий</option>
                            <option value="medium" selected>Средний</option>
                            <option value="high">Высокий</option>
                            <option value="urgent">Срочный</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="subject">Тема обращения</label>
                        <input type="text" name="subject" id="subject" placeholder="Кратко опишите проблему" required>
                    </div>

                    <div class="form-group">
                        <label for="message">Описание проблемы</label>
                        <textarea name="message" id="message" rows="5" placeholder="Подробно опишите вашу проблему или вопрос" required></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="create_ticket" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            Отправить тикет
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('create-ticket-modal')">
                            Отмена
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Ticket View Modal -->
    <div id="ticket-view-modal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>Просмотр тикета</h3>
                <button class="modal-close" onclick="closeModal('ticket-view-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="ticket-view-content">
                <!-- Содержимое загружается через AJAX -->
            </div>
        </div>
    </div>

    <!-- Reply Modal -->
    <div id="reply-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Ответить на тикет</h3>
                <button class="modal-close" onclick="closeModal('reply-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form method="POST" class="reply-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="ticket_id" id="reply-ticket-id">

                    <div class="form-group">
                        <label for="reply-message">Ваше сообщение</label>
                        <textarea name="message" id="reply-message" rows="5" placeholder="Введите ваше сообщение" required></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="send_message" class="btn btn-primary">
                            <i class="fas fa-reply"></i>
                            Отправить ответ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('reply-modal')">
                            Отмена
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <script>
        // Просмотр тикета
        function viewTicket(ticketId) {
            fetch(`api/get-ticket-details.php?id=${ticketId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('ticket-view-content').innerHTML = data.html;
                        openModal('ticket-view-modal');
                    } else {
                        showNotification(data.message || 'Ошибка загрузки тикета', 'error');
                    }
                })
                .catch(error => {
                    showNotification('Ошибка соединения', 'error');
                });
        }

        // Ответ на тикет
        function replyToTicket(ticketId) {
            document.getElementById('reply-ticket-id').value = ticketId;
            document.getElementById('reply-message').value = '';
            openModal('reply-modal');
        }

        // Переключение FAQ
        function toggleFAQ(element) {
            const faqItem = element.closest('.faq-item');
            const answer = faqItem.querySelector('.faq-answer');
            const icon = element.querySelector('i');

            if (faqItem.classList.contains('active')) {
                faqItem.classList.remove('active');
                answer.style.maxHeight = null;
                icon.style.transform = 'rotate(0deg)';
            } else {
                // Закрываем все остальные FAQ
                document.querySelectorAll('.faq-item.active').forEach(item => {
                    item.classList.remove('active');
                    item.querySelector('.faq-answer').style.maxHeight = null;
                    item.querySelector('.faq-question i').style.transform = 'rotate(0deg)';
                });

                faqItem.classList.add('active');
                answer.style.maxHeight = answer.scrollHeight + 'px';
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // Валидация формы создания тикета
        document.querySelector('.ticket-form').addEventListener('submit', function(e) {
            const subject = document.getElementById('subject').value.trim();
            const message = document.getElementById('message').value.trim();

            if (subject.length < 5) {
                e.preventDefault();
                showNotification('Тема должна содержать минимум 5 символов', 'error');
                return;
            }

            if (message.length < 10) {
                e.preventDefault();
                showNotification('Сообщение должно содержать минимум 10 символов', 'error');
                return;
            }
        });

        // Валидация формы ответа
        document.querySelector('.reply-form').addEventListener('submit', function(e) {
            const message = document.getElementById('reply-message').value.trim();

            if (message.length < 5) {
                e.preventDefault();
                showNotification('Сообщение должно содержать минимум 5 символов', 'error');
                return;
            }
        });

        // Автоматическое обновление статуса тикетов (каждые 30 секунд)
        setInterval(function() {
            // Проверяем только если пользователь активен
            if (document.visibilityState === 'visible') {
                fetch('api/get-ticket-updates.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.updates > 0) {
                            // Показываем уведомление о новых ответах
                            showNotification(`У вас ${data.updates} новых ответов в тикетах`, 'info');
                        }
                    })
                    .catch(error => {
                        // Игнорируем ошибки автообновления
                    });
            }
        }, 30000);
    </script>
</body>
</html>
