<?php
/**
 * AstroGenix - Referrals Page
 * Страница реферальной программы
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

// Получение реферальной статистики
try {
    // Статистика рефералов по уровням
    $referral_stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN level = 1 THEN 1 END) as level1_count,
            COUNT(CASE WHEN level = 2 THEN 1 END) as level2_count,
            COUNT(CASE WHEN level = 3 THEN 1 END) as level3_count,
            COALESCE(SUM(CASE WHEN level = 1 THEN 1 ELSE 0 END), 0) as total_level1,
            COALESCE(SUM(CASE WHEN level = 2 THEN 1 ELSE 0 END), 0) as total_level2,
            COALESCE(SUM(CASE WHEN level = 3 THEN 1 ELSE 0 END), 0) as total_level3
        FROM (
            SELECT 1 as level FROM users WHERE referrer_id = ?
            UNION ALL
            SELECT 2 as level FROM users u1 
            JOIN users u2 ON u1.referrer_id = u2.id 
            WHERE u2.referrer_id = ?
            UNION ALL
            SELECT 3 as level FROM users u1 
            JOIN users u2 ON u1.referrer_id = u2.id 
            JOIN users u3 ON u2.referrer_id = u3.id 
            WHERE u3.referrer_id = ?
        ) as referral_levels
    ");
    $referral_stats_stmt->execute([$user['id'], $user['id'], $user['id']]);
    $referral_stats = $referral_stats_stmt->fetch();
    
    // Общая статистика комиссий
    $commission_stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_commissions,
            COALESCE(SUM(amount), 0) as total_earned,
            COALESCE(SUM(CASE WHEN DATE(created_at) = CURDATE() THEN amount ELSE 0 END), 0) as today_earned,
            COALESCE(SUM(CASE WHEN YEARWEEK(created_at) = YEARWEEK(NOW()) THEN amount ELSE 0 END), 0) as week_earned,
            COALESCE(SUM(CASE WHEN MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW()) THEN amount ELSE 0 END), 0) as month_earned
        FROM referral_commissions 
        WHERE user_id = ?
    ");
    $commission_stats_stmt->execute([$user['id']]);
    $commission_stats = $commission_stats_stmt->fetch();
    
    // Последние рефералы (1 уровень)
    $recent_referrals_stmt = $pdo->prepare("
        SELECT u.*, 
               COALESCE(SUM(ui.amount), 0) as total_invested,
               COUNT(ui.id) as investments_count
        FROM users u 
        LEFT JOIN user_investments ui ON u.id = ui.user_id AND ui.status = 'active'
        WHERE u.referrer_id = ? 
        GROUP BY u.id
        ORDER BY u.created_at DESC 
        LIMIT 10
    ");
    $recent_referrals_stmt->execute([$user['id']]);
    $recent_referrals = $recent_referrals_stmt->fetchAll();
    
    // Последние комиссии
    $recent_commissions_stmt = $pdo->prepare("
        SELECT rc.*, u.username as referral_username 
        FROM referral_commissions rc
        JOIN users u ON rc.referral_id = u.id
        WHERE rc.user_id = ? 
        ORDER BY rc.created_at DESC 
        LIMIT 10
    ");
    $recent_commissions_stmt->execute([$user['id']]);
    $recent_commissions = $recent_commissions_stmt->fetchAll();
    
} catch (PDOException $e) {
    $referral_stats = [
        'level1_count' => 0,
        'level2_count' => 0,
        'level3_count' => 0,
        'total_level1' => 0,
        'total_level2' => 0,
        'total_level3' => 0
    ];
    $commission_stats = [
        'total_commissions' => 0,
        'total_earned' => 0,
        'today_earned' => 0,
        'week_earned' => 0,
        'month_earned' => 0
    ];
    $recent_referrals = [];
    $recent_commissions = [];
}

// Реферальная ссылка
$referral_link = getSetting('site_url', 'https://astrogenix.com') . '/register.php?ref=' . $user['referral_code'];

// Настройки реферальной программы
$referral_levels = [
    1 => floatval(getSetting('referral_level1_percent', 10)),
    2 => floatval(getSetting('referral_level2_percent', 5)),
    3 => floatval(getSetting('referral_level3_percent', 2))
];

$page_title = __('referral_program') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('referral_program'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
            </div>
        </header>
        
        <!-- Referrals Content -->
        <div class="dashboard-content">
            <!-- Referral Link Section -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Ваша реферальная ссылка</h3>
                    <div class="card-subtitle">
                        Приглашайте друзей и получайте комиссию с их инвестиций
                    </div>
                </div>
                <div class="card-content">
                    <div class="referral-link-section">
                        <div class="referral-link-display">
                            <input type="text" 
                                   id="referral-link" 
                                   value="<?php echo e($referral_link); ?>" 
                                   readonly 
                                   class="referral-link-input">
                            <button type="button" class="btn btn-primary" onclick="copyReferralLink()">
                                <i class="fas fa-copy"></i>
                                Копировать
                            </button>
                        </div>
                        
                        <div class="referral-code-display">
                            <span class="referral-code-label">Ваш реферальный код:</span>
                            <span class="referral-code"><?php echo e($user['referral_code']); ?></span>
                        </div>
                        
                        <div class="social-share">
                            <h4>Поделиться в социальных сетях:</h4>
                            <div class="social-buttons">
                                <a href="https://t.me/share/url?url=<?php echo urlencode($referral_link); ?>&text=<?php echo urlencode('Присоединяйтесь к AstroGenix - лучшей платформе для стейкинга USDT!'); ?>" 
                                   target="_blank" class="social-btn telegram">
                                    <i class="fab fa-telegram"></i>
                                    Telegram
                                </a>
                                <a href="https://wa.me/?text=<?php echo urlencode('Присоединяйтесь к AstroGenix: ' . $referral_link); ?>" 
                                   target="_blank" class="social-btn whatsapp">
                                    <i class="fab fa-whatsapp"></i>
                                    WhatsApp
                                </a>
                                <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode('Присоединяйтесь к AstroGenix - лучшей платформе для стейкинга USDT! ' . $referral_link); ?>" 
                                   target="_blank" class="social-btn twitter">
                                    <i class="fab fa-twitter"></i>
                                    Twitter
                                </a>
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode($referral_link); ?>" 
                                   target="_blank" class="social-btn facebook">
                                    <i class="fab fa-facebook"></i>
                                    Facebook
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Referral Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $referral_stats['total_level1']; ?></div>
                        <div class="stat-label">Рефералы 1 уровня</div>
                        <div class="stat-sublabel"><?php echo $referral_levels[1]; ?>% комиссия</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $referral_stats['total_level2']; ?></div>
                        <div class="stat-label">Рефералы 2 уровня</div>
                        <div class="stat-sublabel"><?php echo $referral_levels[2]; ?>% комиссия</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $referral_stats['total_level3']; ?></div>
                        <div class="stat-label">Рефералы 3 уровня</div>
                        <div class="stat-sublabel"><?php echo $referral_levels[3]; ?>% комиссия</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($commission_stats['total_earned'], 2); ?></div>
                        <div class="stat-label">Всего заработано</div>
                        <div class="stat-sublabel"><?php echo $commission_stats['total_commissions']; ?> комиссий</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($commission_stats['today_earned'], 2); ?></div>
                        <div class="stat-label">Сегодня</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($commission_stats['week_earned'], 2); ?></div>
                        <div class="stat-label">За неделю</div>
                    </div>
                </div>
            </div>

            <!-- Referral Program Info -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Как работает реферальная программа</h3>
                </div>
                <div class="card-content">
                    <div class="referral-info-grid">
                        <div class="referral-level">
                            <div class="level-header">
                                <div class="level-number">1</div>
                                <div class="level-title">Первый уровень</div>
                                <div class="level-commission"><?php echo $referral_levels[1]; ?>%</div>
                            </div>
                            <div class="level-description">
                                Получайте <?php echo $referral_levels[1]; ?>% с каждой инвестиции ваших прямых рефералов
                            </div>
                        </div>

                        <div class="referral-level">
                            <div class="level-header">
                                <div class="level-number">2</div>
                                <div class="level-title">Второй уровень</div>
                                <div class="level-commission"><?php echo $referral_levels[2]; ?>%</div>
                            </div>
                            <div class="level-description">
                                Получайте <?php echo $referral_levels[2]; ?>% с инвестиций рефералов ваших рефералов
                            </div>
                        </div>

                        <div class="referral-level">
                            <div class="level-header">
                                <div class="level-number">3</div>
                                <div class="level-title">Третий уровень</div>
                                <div class="level-commission"><?php echo $referral_levels[3]; ?>%</div>
                            </div>
                            <div class="level-description">
                                Получайте <?php echo $referral_levels[3]; ?>% с инвестиций рефералов третьего уровня
                            </div>
                        </div>
                    </div>

                    <div class="referral-rules">
                        <h4>Правила программы:</h4>
                        <ul>
                            <li>Комиссия начисляется мгновенно при создании инвестиции</li>
                            <li>Минимальная инвестиция для получения комиссии: $10</li>
                            <li>Комиссия зачисляется на основной баланс</li>
                            <li>Нет ограничений на количество рефералов</li>
                            <li>Комиссия начисляется пожизненно</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Recent Referrals -->
            <?php if (!empty($recent_referrals)): ?>
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Последние рефералы</h3>
                    </div>
                    <div class="card-content">
                        <div class="referrals-table-container">
                            <table class="referrals-table">
                                <thead>
                                    <tr>
                                        <th>Пользователь</th>
                                        <th>Дата регистрации</th>
                                        <th>Инвестиций</th>
                                        <th>Сумма инвестиций</th>
                                        <th>Статус</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_referrals as $referral): ?>
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <div class="username"><?php echo e($referral['username']); ?></div>
                                                    <div class="user-email"><?php echo e($referral['email']); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="date-info">
                                                    <div class="date"><?php echo formatDate($referral['created_at'], 'd.m.Y'); ?></div>
                                                    <div class="time"><?php echo formatDate($referral['created_at'], 'H:i'); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="investments-count"><?php echo $referral['investments_count']; ?></span>
                                            </td>
                                            <td>
                                                <div class="investment-amount">
                                                    $<?php echo formatAmount($referral['total_invested'], 2); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $referral['status']; ?>">
                                                    <?php
                                                    switch($referral['status']) {
                                                        case 'active': echo 'Активен'; break;
                                                        case 'inactive': echo 'Неактивен'; break;
                                                        case 'blocked': echo 'Заблокирован'; break;
                                                        default: echo ucfirst($referral['status']);
                                                    }
                                                    ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Commissions -->
            <?php if (!empty($recent_commissions)): ?>
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Последние комиссии</h3>
                    </div>
                    <div class="card-content">
                        <div class="commissions-table-container">
                            <table class="commissions-table">
                                <thead>
                                    <tr>
                                        <th>Реферал</th>
                                        <th>Уровень</th>
                                        <th>Сумма инвестиции</th>
                                        <th>Комиссия</th>
                                        <th>Дата</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_commissions as $commission): ?>
                                        <tr>
                                            <td>
                                                <div class="referral-info">
                                                    <div class="referral-name"><?php echo e($commission['referral_username']); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="level-badge level-<?php echo $commission['level']; ?>">
                                                    Уровень <?php echo $commission['level']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="investment-amount">
                                                    $<?php echo formatAmount($commission['investment_amount'], 2); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="commission-amount">
                                                    +$<?php echo formatAmount($commission['amount'], 2); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="date-info">
                                                    <div class="date"><?php echo formatDate($commission['created_at'], 'd.m.Y'); ?></div>
                                                    <div class="time"><?php echo formatDate($commission['created_at'], 'H:i'); ?></div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="card-footer">
                            <a href="transactions.php?type=referral" class="btn btn-outline">
                                Посмотреть все комиссии
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>

    <script>
        // Копирование реферальной ссылки
        function copyReferralLink() {
            const linkInput = document.getElementById('referral-link');

            if (navigator.clipboard) {
                navigator.clipboard.writeText(linkInput.value).then(() => {
                    showNotification('Реферальная ссылка скопирована в буфер обмена', 'success');
                });
            } else {
                // Fallback для старых браузеров
                linkInput.select();
                linkInput.setSelectionRange(0, 99999);
                document.execCommand('copy');
                showNotification('Реферальная ссылка скопирована в буфер обмена', 'success');
            }
        }

        // Автоматическое выделение ссылки при клике
        document.getElementById('referral-link').addEventListener('click', function() {
            this.select();
        });
    </script>
</body>
</html>
