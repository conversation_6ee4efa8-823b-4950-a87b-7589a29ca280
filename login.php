<?php
/**
 * AstroGenix - Login Page
 * Страница авторизации пользователей
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Перенаправление авторизованных пользователей
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

// Обработка формы входа
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Проверка CSRF токена
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception(__('csrf_token_mismatch'));
        }
        
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        if (empty($email) || empty($password)) {
            throw new Exception('Email и пароль обязательны для заполнения');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception(__('invalid_email'));
        }
        
        // Попытка входа
        if (loginUser($email, $password)) {
            // Установка cookie для "Запомнить меня"
            if ($remember) {
                $token = bin2hex(random_bytes(32));
                setcookie('remember_token', $token, time() + (86400 * 30), '/'); // 30 дней
                
                // Сохранение токена в базе данных
                $stmt = $pdo->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                $stmt->execute([$token, $_SESSION['user_id']]);
            }
            
            $success = __('login_success');
            
            // Перенаправление
            $redirect = $_GET['redirect'] ?? 'dashboard.php';
            header('Refresh: 1; URL=' . $redirect);
        } else {
            throw new Exception(__('login_failed'));
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

$page_title = __('login') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    <meta name="description" content="Вход в <?php echo e(getSetting('site_name')); ?>">
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/auth.css" rel="stylesheet">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-background">
            <div class="auth-particles"></div>
        </div>
        
        <div class="auth-content">
            <!-- Logo -->
            <div class="auth-logo">
                <a href="<?php echo SITE_URL; ?>">
                    <img src="assets/images/logo.png" alt="<?php echo e(getSetting('site_name')); ?>">
                    <span><?php echo e(getSetting('site_name')); ?></span>
                </a>
            </div>
            
            <!-- Login Form -->
            <div class="auth-card">
                <div class="auth-header">
                    <h1><?php echo __('login'); ?></h1>
                    <p>Войдите в свой аккаунт и продолжайте зарабатывать</p>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo e($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo e($success); ?>
                        <div class="loading-redirect">
                            <div class="spinner"></div>
                            Перенаправление...
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="auth-form" id="login-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-group">
                        <label for="email"><?php echo __('email'); ?></label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo e($_POST['email'] ?? ''); ?>" 
                               placeholder="Введите ваш email"
                               autocomplete="email">
                    </div>
                    
                    <div class="form-group">
                        <label for="password"><?php echo __('password'); ?></label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required
                                   placeholder="Введите ваш пароль"
                                   autocomplete="current-password">
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="remember-forgot">
                        <label class="checkbox-label">
                            <input type="checkbox" name="remember">
                            <span class="checkmark"></span>
                            <?php echo __('remember_me'); ?>
                        </label>
                        <a href="forgot-password.php" class="forgot-password">
                            <?php echo __('forgot_password'); ?>
                        </a>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-block btn-lg">
                        <i class="fas fa-sign-in-alt"></i>
                        <?php echo __('login'); ?>
                    </button>
                </form>
                
                <!-- Social Login -->
                <div class="social-login">
                    <div class="social-divider">
                        <span>или войдите через</span>
                    </div>
                    <div class="social-buttons">
                        <a href="#" class="btn-social" onclick="socialLogin('google')">
                            <i class="fab fa-google"></i>
                            Google
                        </a>
                        <a href="#" class="btn-social" onclick="socialLogin('facebook')">
                            <i class="fab fa-facebook-f"></i>
                            Facebook
                        </a>
                    </div>
                </div>
                
                <div class="auth-footer">
                    <p>Нет аккаунта? <a href="register.php"><?php echo __('register'); ?></a></p>
                </div>
                
                <!-- Quick Stats -->
                <div class="login-stats">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number"><?php 
                                    $user_count = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'")->fetchColumn();
                                    echo number_format($user_count);
                                ?>+</div>
                                <div class="stat-label">Активных пользователей</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number">$<?php 
                                    $total_earned = $pdo->query("SELECT COALESCE(SUM(total_earned), 0) FROM users")->fetchColumn();
                                    echo number_format($total_earned, 0);
                                ?>+</div>
                                <div class="stat-label">Общая прибыль</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number"><?php 
                                    $active_investments = $pdo->query("SELECT COUNT(*) FROM user_investments WHERE status = 'active'")->fetchColumn();
                                    echo number_format($active_investments);
                                ?>+</div>
                                <div class="stat-label">Активных инвестиций</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Security Notice -->
                <div class="security-notice">
                    <div class="notice-header">
                        <i class="fas fa-shield-alt"></i>
                        <span>Безопасность</span>
                    </div>
                    <ul>
                        <li>Используйте сложный пароль</li>
                        <li>Не передавайте данные третьим лицам</li>
                        <li>Всегда выходите из аккаунта на общих устройствах</li>
                        <li>Включите двухфакторную аутентификацию</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/auth.js"></script>
    
    <script>
        // Автофокус на поле email
        document.addEventListener('DOMContentLoaded', function() {
            const emailField = document.getElementById('email');
            if (emailField && !emailField.value) {
                emailField.focus();
            }
        });
        
        // Проверка "Запомнить меня" cookie
        document.addEventListener('DOMContentLoaded', function() {
            const rememberCheckbox = document.querySelector('input[name="remember"]');
            if (localStorage.getItem('remember_login') === 'true') {
                rememberCheckbox.checked = true;
            }
            
            rememberCheckbox.addEventListener('change', function() {
                localStorage.setItem('remember_login', this.checked);
            });
        });
    </script>
</body>
</html>
