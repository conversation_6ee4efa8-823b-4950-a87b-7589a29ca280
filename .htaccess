# AstroGenix .htaccess Configuration - Simplified Version

# Enable URL Rewriting
RewriteEngine On

# Basic Security Headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Compress files for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType image/png "access plus 1 week"
    ExpiresByType image/jpg "access plus 1 week"
    ExpiresByType image/jpeg "access plus 1 week"
    ExpiresByType image/gif "access plus 1 week"
</IfModule>

# Only protect critical files
<Files "database.sql">
    Require all denied
</Files>

<Files "config.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

# Prevent directory browsing
Options -Indexes

# Basic PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300

# Hide PHP version
php_flag expose_php off
