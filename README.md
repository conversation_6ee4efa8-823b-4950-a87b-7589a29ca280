# AstroGenix - USDT Staking Platform

Профессиональная платформа стейкинга USDT с современным дизайном и полным функционалом.

## 🚀 Особенности

### Основной функционал
- **Многоуровневая инвестиционная система** - различные планы с ежедневным начислением прибыли
- **Система задач** - выполняйте задания и получайте награды
- **Многоуровневая реферальная программа** - до 3 уровней с комиссией до 10%
- **Модуль новостей** - система объявлений и обновлений платформы
- **Система тикетов поддержки** - полнофункциональная служба поддержки
- **Бонус за регистрацию** - приветственные бонусы для новых пользователей

### Административные функции
- **Полная админ-панель** для управления всеми аспектами платформы
- **Управление пользователями** и их аккаунтами
- **Управление инвестиционными планами** и пакетами
- **Контроль транзакций** - депозиты/выводы с ручным подтверждением
- **Система новостей** и объявлений
- **Управление задачами** и наградами
- **Настройки сайта** - все конфигурации в базе данных

### Технические характеристики
- **Двуязычный интерфейс** - английский и русский (автоопределение для стран СНГ)
- **Современный дизайн** в стиле Binance/Coinbase
- **Полная адаптивность** для всех устройств
- **Безопасность** - защита от CSRF, SQL-инъекций, XSS
- **Чистый код** - современная архитектура PHP

## 📋 Требования

- PHP 7.4 или выше
- MySQL 5.7 или выше
- Apache/Nginx веб-сервер
- Модули PHP: PDO, mysqli, gd, curl, json

## 🛠 Установка

### 1. Загрузка файлов
Скопируйте все файлы проекта в корневую директорию вашего веб-сервера.

### 2. Настройка базы данных
1. Создайте новую базу данных MySQL:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Импортируйте структуру базы данных:
```bash
mysql -u username -p astrogenix < database.sql
```

### 3. Настройка конфигурации
Отредактируйте файл `config.php`:

```php
// Настройки базы данных
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// URL сайта
define('SITE_URL', 'https://yourdomain.com');
```

### 4. Настройка прав доступа
Установите права на запись для директорий:
```bash
chmod 755 uploads/
chmod 755 uploads/screenshots/
chmod 755 uploads/documents/
```

### 5. Настройка веб-сервера

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^?]*) index.php [NC,L,QSA]

# Security headers
Header always set X-Frame-Options DENY
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

## 🔐 Доступы по умолчанию

### Администратор
- **Логин:** admin
- **Пароль:** admin123
- **URL:** /admin/

### Тестовый пользователь
После установки зарегистрируйте нового пользователя через форму регистрации.

## ⚙️ Настройка

### Основные настройки
Все настройки платформы хранятся в таблице `site_settings` и могут быть изменены через админ-панель:

- Название сайта
- Бонус за регистрацию
- Минимальные суммы депозита/вывода
- Комиссии за вывод
- Реферальные комиссии
- Режим технических работ

### Email настройки
Настройте SMTP в файле `config.php`:
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

### Инвестиционные планы
Управляйте планами через админ-панель:
- Название и описание
- Минимальная/максимальная сумма
- Ежедневная прибыль (%)
- Длительность в днях
- Общая доходность

## 🎨 Кастомизация

### Цвета и стили
Основные цвета определены в CSS переменных в `assets/css/style.css`:
```css
:root {
    --primary-color: #f0b90b;
    --secondary-color: #1e2329;
    --accent-color: #00d4aa;
    /* ... */
}
```

### Логотип
Замените файлы в директории `assets/images/`:
- `logo.png` - основной логотип
- `logo.svg` - векторная версия
- `favicon.ico` - иконка сайта

### Языки
Добавьте новые языки, создав файлы в директории `languages/`:
- `languages/de.php` - немецкий
- `languages/es.php` - испанский
- и т.д.

## 🔧 API Endpoints

### Пользовательские API
- `api/change-language.php` - смена языка
- `api/get-balance.php` - получение баланса
- `api/get-stats.php` - статистика пользователя
- `api/notifications.php` - уведомления

### Административные API
- `admin/api/users.php` - управление пользователями
- `admin/api/transactions.php` - управление транзакциями
- `admin/api/settings.php` - настройки сайта

## 📱 Мобильная версия

Платформа полностью адаптивна и работает на всех устройствах:
- Адаптивная навигация
- Оптимизированные формы
- Touch-friendly интерфейс
- Быстрая загрузка

## 🛡 Безопасность

### Реализованные меры
- CSRF защита для всех форм
- Подготовленные SQL запросы
- XSS фильтрация
- Валидация всех входных данных
- Хеширование паролей (bcrypt)
- Ограничение попыток входа

### Рекомендации
- Используйте HTTPS
- Регулярно обновляйте PHP и MySQL
- Настройте файрвол
- Делайте резервные копии
- Мониторьте логи

## 📊 Мониторинг

### Логи
Система ведет логи всех важных действий:
- Входы/выходы пользователей
- Транзакции
- Административные действия
- Ошибки системы

### Статистика
Доступна детальная статистика:
- Количество пользователей
- Объемы депозитов/выводов
- Активность по дням
- Реферальная статистика

## 🚀 Производительность

### Оптимизация
- Минификация CSS/JS
- Сжатие изображений
- Кеширование запросов
- CDN для статических файлов

### Масштабирование
- Поддержка нескольких серверов
- Балансировка нагрузки
- Репликация базы данных
- Redis для сессий

## 📞 Поддержка

### Документация
- Подробные комментарии в коде
- API документация
- Руководство администратора

### Техническая поддержка
Для получения поддержки:
1. Проверьте документацию
2. Изучите логи ошибок
3. Обратитесь к разработчику

## 📄 Лицензия

Этот проект создан для демонстрационных целей. Использование в коммерческих целях требует отдельного соглашения.

## 🔄 Обновления

### Версия 1.0.0
- Первый релиз
- Базовый функционал
- Админ-панель
- Двуязычность

### Планируемые обновления
- Интеграция с реальными платежными системами
- Мобильное приложение
- Дополнительные языки
- Расширенная аналитика

---

**Внимание:** Это демонстрационная платформа. Для использования в продакшене требуется дополнительная настройка безопасности и интеграция с реальными платежными системами.
