<?php
/**
 * AstroGenix - Registration Page
 * User Registration Page
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';
require_once 'includes/language.php';

// Перенаправление авторизованных пользователей
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';
$referrer_id = null;

// Обработка реферальной ссылки
if (isset($_GET['ref'])) {
    $referrer = getUserByReferralCode($_GET['ref']);
    if ($referrer) {
        $referrer_id = $referrer['id'];
        $_SESSION['referrer_id'] = $referrer_id;
    }
} elseif (isset($_SESSION['referrer_id'])) {
    $referrer_id = $_SESSION['referrer_id'];
}

// Обработка формы регистрации
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Проверка CSRF токена
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception(__('csrf_token_mismatch'));
        }
        
        // Валидация данных
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $first_name = trim($_POST['first_name'] ?? '');
        $last_name = trim($_POST['last_name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $country = trim($_POST['country'] ?? '');
        $agree_terms = isset($_POST['agree_terms']);
        
        // Проверки
        if (empty($username) || empty($email) || empty($password)) {
            throw new Exception(__('error_required_field'));
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception(__('invalid_email'));
        }

        if (strlen($password) < 6) {
            throw new Exception(__('weak_password'));
        }

        if ($password !== $confirm_password) {
            throw new Exception(__('passwords_not_match'));
        }

        if (!$agree_terms) {
            throw new Exception(__('terms_agreement'));
        }
        
        // Регистрация пользователя
        $user_data = [
            'username' => $username,
            'email' => $email,
            'password' => $password,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'phone' => $phone,
            'country' => $country,
            'referrer_id' => $referrer_id,
            'language' => getCurrentLanguage()
        ];
        
        $user_id = registerUser($user_data);
        
        // Автоматический вход после регистрации
        $_SESSION['user_id'] = $user_id;
        $_SESSION['username'] = $username;
        
        // Очистка реферальной информации из сессии
        unset($_SESSION['referrer_id']);
        
        $success = __('register_success');
        
        // Перенаправление через 2 секунды
        header('Refresh: 2; URL=dashboard.php');
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

$page_title = __('sign_up_title') . ' - ' . __('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars(__('sign_up_title')); ?>">

    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="assets/css/modern.css" rel="stylesheet">
    <link href="assets/css/auth.css" rel="stylesheet">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-background">
            <div class="auth-gradient"></div>
            <div class="auth-pattern"></div>
        </div>

        <div class="auth-content">
            <!-- Logo -->
            <div class="auth-logo">
                <a href="<?php echo SITE_URL; ?>">
                    <img src="assets/images/logo.svg" alt="<?php echo __('site_name'); ?>">
                </a>
            </div>

            <!-- Registration Form -->
            <div class="auth-card">
                <div class="auth-header">
                    <h1><?php echo __('sign_up_title'); ?></h1>
                    <p><?php echo __('hero_description'); ?></p>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo e($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo e($success); ?>
                        <div class="loading-redirect">
                            <div class="spinner"></div>
                            Перенаправление в личный кабинет...
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($referrer_id): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-user-friends"></i>
                        <?php echo __('invite_friends_earn'); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="auth-form" id="register-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name"><?php echo __('first_name'); ?></label>
                            <input type="text" id="first_name" name="first_name"
                                   value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                                   placeholder="<?php echo __('first_name'); ?>">
                        </div>
                        <div class="form-group">
                            <label for="last_name"><?php echo __('last_name'); ?></label>
                            <input type="text" id="last_name" name="last_name"
                                   value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                                   placeholder="<?php echo __('last_name'); ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="username"><?php echo __('username'); ?> *</label>
                        <input type="text" id="username" name="username" required
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               placeholder="<?php echo __('username'); ?>">
                    </div>

                    <div class="form-group">
                        <label for="email"><?php echo __('email'); ?> *</label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               placeholder="<?php echo __('email'); ?>">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone"><?php echo __('phone'); ?></label>
                            <input type="tel" id="phone" name="phone"
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                   placeholder="+1234567890">
                        </div>
                        <div class="form-group">
                            <label for="country"><?php echo __('country'); ?></label>
                            <select id="country" name="country">
                                <option value=""><?php echo __('country'); ?></option>
                                <option value="US" <?php echo ($_POST['country'] ?? '') === 'US' ? 'selected' : ''; ?>>United States</option>
                                <option value="RU" <?php echo ($_POST['country'] ?? '') === 'RU' ? 'selected' : ''; ?>>Russia</option>
                                <option value="UA" <?php echo ($_POST['country'] ?? '') === 'UA' ? 'selected' : ''; ?>>Ukraine</option>
                                <option value="BY" <?php echo ($_POST['country'] ?? '') === 'BY' ? 'selected' : ''; ?>>Belarus</option>
                                <option value="KZ" <?php echo ($_POST['country'] ?? '') === 'KZ' ? 'selected' : ''; ?>>Kazakhstan</option>
                                <option value="DE" <?php echo ($_POST['country'] ?? '') === 'DE' ? 'selected' : ''; ?>>Germany</option>
                                <option value="FR" <?php echo ($_POST['country'] ?? '') === 'FR' ? 'selected' : ''; ?>>France</option>
                                <option value="GB" <?php echo ($_POST['country'] ?? '') === 'GB' ? 'selected' : ''; ?>>United Kingdom</option>
                                <option value="CA" <?php echo ($_POST['country'] ?? '') === 'CA' ? 'selected' : ''; ?>>Canada</option>
                                <option value="AU" <?php echo ($_POST['country'] ?? '') === 'AU' ? 'selected' : ''; ?>>Australia</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password"><?php echo __('password'); ?> *</label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required
                                   placeholder="<?php echo __('weak_password'); ?>">
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="password-strength"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password"><?php echo __('confirm_password'); ?> *</label>
                        <div class="password-input">
                            <input type="password" id="confirm_password" name="confirm_password" required
                                   placeholder="<?php echo __('confirm_password'); ?>">
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="agree_terms" required>
                            <span class="checkmark"></span>
                            <?php echo __('terms_agreement'); ?> <a href="terms.php" target="_blank"><?php echo __('terms_of_service'); ?></a>
                            <?php echo __('privacy_policy'); ?> <a href="privacy.php" target="_blank"><?php echo __('privacy_policy'); ?></a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-full btn-lg">
                        <i class="fas fa-user-plus"></i>
                        <?php echo __('create_account'); ?>
                    </button>
                </form>

                <div class="auth-footer">
                    <p><?php echo __('already_have_account'); ?> <a href="login.php"><?php echo __('login'); ?></a></p>
                </div>

                <!-- Registration Benefits -->
                <div class="registration-benefits">
                    <h3><?php echo __('get_started'); ?>:</h3>
                    <ul>
                        <li><i class="fas fa-gift"></i> <?php echo __('registration_bonus', ['amount' => '$' . (getSetting('registration_bonus', 10))]); ?></li>
                        <li><i class="fas fa-chart-line"></i> <?php echo __('investment_plans'); ?></li>
                        <li><i class="fas fa-users"></i> <?php echo __('referral_program'); ?></li>
                        <li><i class="fas fa-headset"></i> <?php echo __('feature_support'); ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // Language switcher
        function changeLanguage(lang) {
            window.location.href = '?lang=' + lang;
        }

        // Password toggle
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const toggle = field.nextElementSibling;
            const icon = toggle.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Password strength checker
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('password');
            const strengthIndicator = document.getElementById('password-strength');

            if (passwordField && strengthIndicator) {
                passwordField.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;

                    if (password.length >= 6) strength++;
                    if (password.match(/[a-z]/)) strength++;
                    if (password.match(/[A-Z]/)) strength++;
                    if (password.match(/[0-9]/)) strength++;
                    if (password.match(/[^a-zA-Z0-9]/)) strength++;

                    strengthIndicator.className = 'password-strength';
                    if (strength < 3) {
                        strengthIndicator.classList.add('weak');
                    } else if (strength < 5) {
                        strengthIndicator.classList.add('medium');
                    } else {
                        strengthIndicator.classList.add('strong');
                    }
                });
            }
        });
    </script>
</body>
</html>
