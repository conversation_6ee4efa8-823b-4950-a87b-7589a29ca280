<?php
/**
 * AstroGenix - Registration Page
 * Страница регистрации пользователей
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Перенаправление авторизованных пользователей
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';
$referrer_id = null;

// Обработка реферальной ссылки
if (isset($_GET['ref'])) {
    $referrer = getUserByReferralCode($_GET['ref']);
    if ($referrer) {
        $referrer_id = $referrer['id'];
        $_SESSION['referrer_id'] = $referrer_id;
    }
} elseif (isset($_SESSION['referrer_id'])) {
    $referrer_id = $_SESSION['referrer_id'];
}

// Обработка формы регистрации
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Проверка CSRF токена
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception(__('csrf_token_mismatch'));
        }
        
        // Валидация данных
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $first_name = trim($_POST['first_name'] ?? '');
        $last_name = trim($_POST['last_name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $country = trim($_POST['country'] ?? '');
        $agree_terms = isset($_POST['agree_terms']);
        
        // Проверки
        if (empty($username) || empty($email) || empty($password)) {
            throw new Exception('Все обязательные поля должны быть заполнены');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception(__('invalid_email'));
        }
        
        if (strlen($password) < 6) {
            throw new Exception(__('weak_password'));
        }
        
        if ($password !== $confirm_password) {
            throw new Exception(__('passwords_not_match'));
        }
        
        if (!$agree_terms) {
            throw new Exception('Вы должны согласиться с условиями использования');
        }
        
        // Регистрация пользователя
        $user_data = [
            'username' => $username,
            'email' => $email,
            'password' => $password,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'phone' => $phone,
            'country' => $country,
            'referrer_id' => $referrer_id,
            'language' => $language
        ];
        
        $user_id = registerUser($user_data);
        
        // Автоматический вход после регистрации
        $_SESSION['user_id'] = $user_id;
        $_SESSION['username'] = $username;
        
        // Очистка реферальной информации из сессии
        unset($_SESSION['referrer_id']);
        
        $success = __('register_success');
        
        // Перенаправление через 2 секунды
        header('Refresh: 2; URL=dashboard.php');
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

$page_title = __('register') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    <meta name="description" content="Регистрация в <?php echo e(getSetting('site_name')); ?>">
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/auth.css" rel="stylesheet">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-background">
            <div class="auth-particles"></div>
        </div>
        
        <div class="auth-content">
            <!-- Logo -->
            <div class="auth-logo">
                <a href="<?php echo SITE_URL; ?>">
                    <img src="assets/images/logo.png" alt="<?php echo e(getSetting('site_name')); ?>">
                    <span><?php echo e(getSetting('site_name')); ?></span>
                </a>
            </div>
            
            <!-- Registration Form -->
            <div class="auth-card">
                <div class="auth-header">
                    <h1><?php echo __('register'); ?></h1>
                    <p>Создайте аккаунт и начните зарабатывать уже сегодня</p>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo e($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo e($success); ?>
                        <div class="loading-redirect">
                            <div class="spinner"></div>
                            Перенаправление в личный кабинет...
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($referrer_id): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-user-friends"></i>
                        Вы регистрируетесь по реферальной ссылке. Получите дополнительные бонусы!
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="auth-form" id="register-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name"><?php echo __('first_name'); ?></label>
                            <input type="text" id="first_name" name="first_name" 
                                   value="<?php echo e($_POST['first_name'] ?? ''); ?>" 
                                   placeholder="Введите ваше имя">
                        </div>
                        <div class="form-group">
                            <label for="last_name"><?php echo __('last_name'); ?></label>
                            <input type="text" id="last_name" name="last_name" 
                                   value="<?php echo e($_POST['last_name'] ?? ''); ?>" 
                                   placeholder="Введите вашу фамилию">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="username"><?php echo __('username'); ?> *</label>
                        <input type="text" id="username" name="username" required
                               value="<?php echo e($_POST['username'] ?? ''); ?>" 
                               placeholder="Введите имя пользователя">
                    </div>
                    
                    <div class="form-group">
                        <label for="email"><?php echo __('email'); ?> *</label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo e($_POST['email'] ?? ''); ?>" 
                               placeholder="Введите ваш email">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone"><?php echo __('phone'); ?></label>
                            <input type="tel" id="phone" name="phone" 
                                   value="<?php echo e($_POST['phone'] ?? ''); ?>" 
                                   placeholder="+1234567890">
                        </div>
                        <div class="form-group">
                            <label for="country"><?php echo __('country'); ?></label>
                            <select id="country" name="country">
                                <option value="">Выберите страну</option>
                                <option value="US" <?php echo ($_POST['country'] ?? '') === 'US' ? 'selected' : ''; ?>>United States</option>
                                <option value="RU" <?php echo ($_POST['country'] ?? '') === 'RU' ? 'selected' : ''; ?>>Russia</option>
                                <option value="UA" <?php echo ($_POST['country'] ?? '') === 'UA' ? 'selected' : ''; ?>>Ukraine</option>
                                <option value="BY" <?php echo ($_POST['country'] ?? '') === 'BY' ? 'selected' : ''; ?>>Belarus</option>
                                <option value="KZ" <?php echo ($_POST['country'] ?? '') === 'KZ' ? 'selected' : ''; ?>>Kazakhstan</option>
                                <option value="DE" <?php echo ($_POST['country'] ?? '') === 'DE' ? 'selected' : ''; ?>>Germany</option>
                                <option value="FR" <?php echo ($_POST['country'] ?? '') === 'FR' ? 'selected' : ''; ?>>France</option>
                                <option value="GB" <?php echo ($_POST['country'] ?? '') === 'GB' ? 'selected' : ''; ?>>United Kingdom</option>
                                <option value="CA" <?php echo ($_POST['country'] ?? '') === 'CA' ? 'selected' : ''; ?>>Canada</option>
                                <option value="AU" <?php echo ($_POST['country'] ?? '') === 'AU' ? 'selected' : ''; ?>>Australia</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password"><?php echo __('password'); ?> *</label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required
                                   placeholder="Минимум 6 символов">
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="password-strength"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password"><?php echo __('confirm_password'); ?> *</label>
                        <div class="password-input">
                            <input type="password" id="confirm_password" name="confirm_password" required
                                   placeholder="Повторите пароль">
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="agree_terms" required>
                            <span class="checkmark"></span>
                            Я согласен с <a href="terms.php" target="_blank">условиями использования</a> 
                            и <a href="privacy.php" target="_blank">политикой конфиденциальности</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-block btn-lg">
                        <i class="fas fa-user-plus"></i>
                        <?php echo __('register'); ?>
                    </button>
                </form>
                
                <div class="auth-footer">
                    <p>Уже есть аккаунт? <a href="login.php"><?php echo __('login'); ?></a></p>
                </div>
                
                <!-- Registration Benefits -->
                <div class="registration-benefits">
                    <h3>Преимущества регистрации:</h3>
                    <ul>
                        <li><i class="fas fa-gift"></i> Бонус $<?php echo getSetting('registration_bonus', 10); ?> за регистрацию</li>
                        <li><i class="fas fa-chart-line"></i> Доступ к инвестиционным планам</li>
                        <li><i class="fas fa-users"></i> Реферальная программа до 10%</li>
                        <li><i class="fas fa-headset"></i> Поддержка 24/7</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/auth.js"></script>
</body>
</html>
