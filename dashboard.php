<?php
/**
 * AstroGenix - User Dashboard
 * Панель управления пользователя
 */

define('ASTROGENIX_INIT', true);
require_once 'config.php';

// Проверка авторизации
requireAuth();

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

// Получение статистики пользователя
try {
    // Активные инвестиции
    $stmt = $pdo->prepare("
        SELECT ui.*, ip.name as plan_name, ip.daily_profit as plan_daily_profit
        FROM user_investments ui 
        JOIN investment_plans ip ON ui.plan_id = ip.id 
        WHERE ui.user_id = ? AND ui.status = 'active' 
        ORDER BY ui.created_at DESC
    ");
    $stmt->execute([$user['id']]);
    $active_investments = $stmt->fetchAll();
    
    // Последние транзакции
    $stmt = $pdo->prepare("
        SELECT * FROM transactions 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$user['id']]);
    $recent_transactions = $stmt->fetchAll();
    
    // Рефералы
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total_referrals,
               COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END), 0) as monthly_referrals
        FROM users 
        WHERE referrer_id = ?
    ");
    $stmt->execute([$user['id']]);
    $referral_stats = $stmt->fetch();
    
    // Доходы с рефералов за последние 30 дней
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(commission_amount), 0) as monthly_referral_earnings
        FROM referral_commissions 
        WHERE referrer_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stmt->execute([$user['id']]);
    $monthly_referral_earnings = $stmt->fetchColumn();
    
    // Активные задачи
    $stmt = $pdo->prepare("
        SELECT t.*, ut.status as user_status, ut.completed_at, ut.claimed_at
        FROM tasks t
        LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
        WHERE t.status = 'active'
        ORDER BY t.sort_order ASC
    ");
    $stmt->execute([$user['id']]);
    $tasks = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $active_investments = [];
    $recent_transactions = [];
    $referral_stats = ['total_referrals' => 0, 'monthly_referrals' => 0];
    $monthly_referral_earnings = 0;
    $tasks = [];
}

$page_title = __('dashboard') . ' - ' . getSetting('site_name');
?>
<!DOCTYPE html>
<html lang="<?php echo $language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($page_title); ?></title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="assets/images/logo.png" alt="<?php echo e(getSetting('site_name')); ?>">
                <span><?php echo e(getSetting('site_name')); ?></span>
            </div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span><?php echo __('dashboard'); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="investments.php" class="nav-link">
                        <i class="fas fa-chart-line"></i>
                        <span><?php echo __('investments'); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="transactions.php" class="nav-link">
                        <i class="fas fa-exchange-alt"></i>
                        <span><?php echo __('transactions'); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="referrals.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span><?php echo __('referrals'); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="tasks.php" class="nav-link">
                        <i class="fas fa-tasks"></i>
                        <span><?php echo __('tasks'); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="support.php" class="nav-link">
                        <i class="fas fa-headset"></i>
                        <span><?php echo __('support'); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="profile.php" class="nav-link">
                        <i class="fas fa-user"></i>
                        <span><?php echo __('profile'); ?></span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo e($user['username']); ?></div>
                    <div class="user-email"><?php echo e($user['email']); ?></div>
                </div>
            </div>
            <a href="logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <?php echo __('logout'); ?>
            </a>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><?php echo __('dashboard'); ?></h1>
            </div>
            
            <div class="header-right">
                <div class="balance-display">
                    <div class="balance-label"><?php echo __('balance'); ?></div>
                    <div class="balance-amount">$<?php echo formatAmount($user['balance'], 2); ?></div>
                </div>
                
                <div class="header-actions">
                    <a href="deposit.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i>
                        <?php echo __('deposit'); ?>
                    </a>
                    <a href="withdraw.php" class="btn btn-outline btn-sm">
                        <i class="fas fa-minus"></i>
                        <?php echo __('withdrawal'); ?>
                    </a>
                </div>
                
                <div class="notifications-dropdown">
                    <button class="notifications-toggle">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($user['balance'], 2); ?></div>
                        <div class="stat-label"><?php echo __('available'); ?> <?php echo __('balance'); ?></div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($user['total_invested'], 2); ?></div>
                        <div class="stat-label"><?php echo __('total_invested'); ?></div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">$<?php echo formatAmount($user['total_earned'], 2); ?></div>
                        <div class="stat-label"><?php echo __('total_earned'); ?></div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo $referral_stats['total_referrals']; ?></div>
                        <div class="stat-label"><?php echo __('total_referrals'); ?></div>
                    </div>
                </div>
            </div>
            
            <!-- Main Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Active Investments -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><?php echo __('active_investments'); ?></h3>
                        <a href="investments.php" class="card-action"><?php echo __('view'); ?> <?php echo __('all'); ?></a>
                    </div>
                    <div class="card-content">
                        <?php if (empty($active_investments)): ?>
                            <div class="empty-state">
                                <i class="fas fa-chart-line"></i>
                                <p>У вас пока нет активных инвестиций</p>
                                <a href="invest.php" class="btn btn-primary"><?php echo __('invest_now'); ?></a>
                            </div>
                        <?php else: ?>
                            <div class="investments-list">
                                <?php foreach (array_slice($active_investments, 0, 3) as $investment): ?>
                                    <div class="investment-item">
                                        <div class="investment-info">
                                            <div class="investment-plan"><?php echo e($investment['plan_name']); ?></div>
                                            <div class="investment-amount">$<?php echo formatAmount($investment['amount'], 2); ?></div>
                                        </div>
                                        <div class="investment-stats">
                                            <div class="daily-profit">
                                                +$<?php echo formatAmount($investment['daily_profit'], 2); ?>/день
                                            </div>
                                            <div class="total-earned">
                                                Заработано: $<?php echo formatAmount($investment['total_earned'], 2); ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Recent Transactions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><?php echo __('transaction_history'); ?></h3>
                        <a href="transactions.php" class="card-action"><?php echo __('view'); ?> <?php echo __('all'); ?></a>
                    </div>
                    <div class="card-content">
                        <?php if (empty($recent_transactions)): ?>
                            <div class="empty-state">
                                <i class="fas fa-exchange-alt"></i>
                                <p>История транзакций пуста</p>
                            </div>
                        <?php else: ?>
                            <div class="transactions-list">
                                <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                                    <div class="transaction-item">
                                        <div class="transaction-icon">
                                            <i class="fas fa-<?php 
                                                echo $transaction['type'] === 'deposit' ? 'arrow-down' : 
                                                    ($transaction['type'] === 'withdrawal' ? 'arrow-up' : 
                                                    ($transaction['type'] === 'profit' ? 'coins' : 'exchange-alt')); 
                                            ?>"></i>
                                        </div>
                                        <div class="transaction-info">
                                            <div class="transaction-type"><?php echo __(strtolower($transaction['type'])); ?></div>
                                            <div class="transaction-date"><?php echo formatDate($transaction['created_at']); ?></div>
                                        </div>
                                        <div class="transaction-amount <?php echo $transaction['type'] === 'withdrawal' ? 'negative' : 'positive'; ?>">
                                            <?php echo $transaction['type'] === 'withdrawal' ? '-' : '+'; ?>$<?php echo formatAmount($transaction['amount'], 2); ?>
                                        </div>
                                        <div class="transaction-status status-<?php echo $transaction['status']; ?>">
                                            <?php echo __(strtolower($transaction['status'])); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Быстрые действия</h3>
                    </div>
                    <div class="card-content">
                        <div class="quick-actions">
                            <a href="invest.php" class="quick-action">
                                <div class="action-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="action-text">
                                    <div class="action-title"><?php echo __('invest_now'); ?></div>
                                    <div class="action-desc">Выберите план инвестирования</div>
                                </div>
                            </a>
                            
                            <a href="referrals.php" class="quick-action">
                                <div class="action-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="action-text">
                                    <div class="action-title"><?php echo __('invite_friends'); ?></div>
                                    <div class="action-desc">Получайте комиссию до 10%</div>
                                </div>
                            </a>
                            
                            <a href="tasks.php" class="quick-action">
                                <div class="action-icon">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div class="action-text">
                                    <div class="action-title"><?php echo __('complete_task'); ?></div>
                                    <div class="action-desc">Выполняйте задачи и получайте бонусы</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Referral Stats -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><?php echo __('referral_program'); ?></h3>
                        <a href="referrals.php" class="card-action"><?php echo __('view'); ?> <?php echo __('all'); ?></a>
                    </div>
                    <div class="card-content">
                        <div class="referral-stats">
                            <div class="referral-stat">
                                <div class="stat-number"><?php echo $referral_stats['total_referrals']; ?></div>
                                <div class="stat-label">Всего рефералов</div>
                            </div>
                            <div class="referral-stat">
                                <div class="stat-number"><?php echo $referral_stats['monthly_referrals']; ?></div>
                                <div class="stat-label">За месяц</div>
                            </div>
                            <div class="referral-stat">
                                <div class="stat-number">$<?php echo formatAmount($monthly_referral_earnings, 2); ?></div>
                                <div class="stat-label">Доход за месяц</div>
                            </div>
                        </div>
                        
                        <div class="referral-link-section">
                            <label>Ваша реферальная ссылка:</label>
                            <div class="referral-link-input">
                                <input type="text" readonly value="<?php echo SITE_URL; ?>/register.php?ref=<?php echo $user['referral_code']; ?>" id="referral-link">
                                <button class="copy-btn" onclick="copyReferralLink()">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>
    
    <script>
        function copyReferralLink() {
            const input = document.getElementById('referral-link');
            input.select();
            document.execCommand('copy');
            showNotification('Реферальная ссылка скопирована!', 'success');
        }
    </script>
</body>
</html>
