/**
 * AstroGenix Dashboard JavaScript
 * Функции для панели управления пользователя
 */

document.addEventListener('DOMContentLoaded', function() {
    initSidebar();
    initNotifications();
    initCharts();
    initRealTimeUpdates();
    initQuickActions();
    
    console.log('Dashboard initialized');
});

/**
 * Инициализация боковой панели
 */
function initSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    
    // Мобильное меню
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
    
    // Закрытие сайдбара
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.remove('active');
        });
    }
    
    // Закрытие при клике вне сайдбара на мобильных
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024) {
            if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        }
    });
    
    // Активная ссылка навигации
    const currentPage = window.location.pathname.split('/').pop();
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'dashboard.php')) {
            link.parentElement.classList.add('active');
        }
    });
}

/**
 * Инициализация уведомлений
 */
function initNotifications() {
    const notificationsToggle = document.querySelector('.notifications-toggle');
    
    if (notificationsToggle) {
        notificationsToggle.addEventListener('click', function() {
            toggleNotificationsDropdown();
        });
    }
    
    // Загрузка уведомлений
    loadNotifications();
    
    // Обновление уведомлений каждые 30 секунд
    setInterval(loadNotifications, 30000);
}

/**
 * Переключение выпадающего меню уведомлений
 */
function toggleNotificationsDropdown() {
    // Создание выпадающего меню, если его нет
    let dropdown = document.querySelector('.notifications-dropdown-menu');
    
    if (!dropdown) {
        dropdown = createNotificationsDropdown();
        document.querySelector('.notifications-dropdown').appendChild(dropdown);
    }
    
    dropdown.classList.toggle('show');
}

/**
 * Создание выпадающего меню уведомлений
 */
function createNotificationsDropdown() {
    const dropdown = document.createElement('div');
    dropdown.className = 'notifications-dropdown-menu';
    dropdown.innerHTML = `
        <div class="notifications-header">
            <h4>Уведомления</h4>
            <button class="mark-all-read">Отметить все как прочитанные</button>
        </div>
        <div class="notifications-list" id="notifications-list">
            <div class="loading">Загрузка...</div>
        </div>
        <div class="notifications-footer">
            <a href="notifications.php">Все уведомления</a>
        </div>
    `;
    
    return dropdown;
}

/**
 * Загрузка уведомлений
 */
function loadNotifications() {
    fetch('api/notifications.php')
        .then(response => response.json())
        .then(data => {
            updateNotificationsBadge(data.unread_count);
            updateNotificationsList(data.notifications);
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
        });
}

/**
 * Обновление счетчика уведомлений
 */
function updateNotificationsBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }
}

/**
 * Обновление списка уведомлений
 */
function updateNotificationsList(notifications) {
    const list = document.getElementById('notifications-list');
    if (!list) return;
    
    if (notifications.length === 0) {
        list.innerHTML = '<div class="no-notifications">Нет новых уведомлений</div>';
        return;
    }
    
    list.innerHTML = notifications.map(notification => `
        <div class="notification-item ${notification.read ? 'read' : 'unread'}">
            <div class="notification-icon">
                <i class="fas fa-${getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${timeAgo(notification.created_at)}</div>
            </div>
        </div>
    `).join('');
}

/**
 * Получение иконки для типа уведомления
 */
function getNotificationIcon(type) {
    const icons = {
        'deposit': 'arrow-down',
        'withdrawal': 'arrow-up',
        'profit': 'coins',
        'referral': 'users',
        'system': 'bell',
        'security': 'shield-alt'
    };
    
    return icons[type] || 'bell';
}

/**
 * Инициализация графиков
 */
function initCharts() {
    // График баланса
    const balanceChart = document.getElementById('balance-chart');
    if (balanceChart) {
        createBalanceChart(balanceChart);
    }
    
    // График инвестиций
    const investmentChart = document.getElementById('investment-chart');
    if (investmentChart) {
        createInvestmentChart(investmentChart);
    }
}

/**
 * Создание графика баланса
 */
function createBalanceChart(canvas) {
    const ctx = canvas.getContext('2d');
    
    // Генерация данных за последние 30 дней
    const data = generateBalanceData();
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                label: 'Баланс',
                data: data.values,
                borderColor: '#f0b90b',
                backgroundColor: 'rgba(240, 185, 11, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#848e9c'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#848e9c',
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
}

/**
 * Генерация данных баланса
 */
function generateBalanceData() {
    const labels = [];
    const values = [];
    const today = new Date();
    
    for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('ru-RU', { month: 'short', day: 'numeric' }));
        
        // Симуляция роста баланса
        const baseValue = 1000;
        const growth = (29 - i) * 10;
        const variation = (Math.random() - 0.5) * 50;
        values.push(Math.max(0, baseValue + growth + variation));
    }
    
    return { labels, values };
}

/**
 * Инициализация обновлений в реальном времени
 */
function initRealTimeUpdates() {
    // Обновление баланса каждые 60 секунд
    setInterval(updateBalance, 60000);
    
    // Обновление статистики каждые 2 минуты
    setInterval(updateStats, 120000);
    
    // Проверка новых транзакций каждые 30 секунд
    setInterval(checkNewTransactions, 30000);
}

/**
 * Обновление баланса
 */
function updateBalance() {
    fetch('api/get-balance.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const balanceElement = document.querySelector('.balance-amount');
                if (balanceElement) {
                    animateValue(balanceElement, parseFloat(balanceElement.textContent.replace('$', '')), data.balance);
                }
            }
        })
        .catch(error => {
            console.error('Error updating balance:', error);
        });
}

/**
 * Анимация изменения значения
 */
function animateValue(element, start, end, duration = 1000) {
    const startTime = performance.now();
    const difference = end - start;
    
    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (difference * progress);
        element.textContent = '$' + current.toFixed(2);
        
        if (progress < 1) {
            requestAnimationFrame(updateValue);
        }
    }
    
    requestAnimationFrame(updateValue);
}

/**
 * Обновление статистики
 */
function updateStats() {
    fetch('api/get-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatCards(data.stats);
            }
        })
        .catch(error => {
            console.error('Error updating stats:', error);
        });
}

/**
 * Обновление карточек статистики
 */
function updateStatCards(stats) {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        const valueElement = card.querySelector('.stat-value');
        if (valueElement && stats[index]) {
            const currentValue = parseFloat(valueElement.textContent.replace(/[$,]/g, ''));
            const newValue = stats[index].value;
            
            if (currentValue !== newValue) {
                animateValue(valueElement, currentValue, newValue);
            }
        }
    });
}

/**
 * Проверка новых транзакций
 */
function checkNewTransactions() {
    const lastCheck = localStorage.getItem('lastTransactionCheck') || '0';
    
    fetch(`api/check-transactions.php?since=${lastCheck}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.new_transactions > 0) {
                showNotification(`У вас ${data.new_transactions} новых транзакций`, 'info');
                localStorage.setItem('lastTransactionCheck', Date.now().toString());
            }
        })
        .catch(error => {
            console.error('Error checking transactions:', error);
        });
}

/**
 * Инициализация быстрых действий
 */
function initQuickActions() {
    // Быстрое пополнение
    const quickDepositBtns = document.querySelectorAll('[data-quick-deposit]');
    quickDepositBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const amount = this.getAttribute('data-quick-deposit');
            quickDeposit(amount);
        });
    });
    
    // Быстрый вывод
    const quickWithdrawBtns = document.querySelectorAll('[data-quick-withdraw]');
    quickWithdrawBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const amount = this.getAttribute('data-quick-withdraw');
            quickWithdraw(amount);
        });
    });
}

/**
 * Быстрое пополнение
 */
function quickDeposit(amount) {
    if (confirm(`Пополнить баланс на $${amount}?`)) {
        window.location.href = `deposit.php?amount=${amount}`;
    }
}

/**
 * Быстрый вывод
 */
function quickWithdraw(amount) {
    if (confirm(`Вывести $${amount} с баланса?`)) {
        window.location.href = `withdraw.php?amount=${amount}`;
    }
}

/**
 * Копирование реферальной ссылки
 */
function copyReferralLink() {
    const input = document.getElementById('referral-link');
    if (input) {
        input.select();
        document.execCommand('copy');
        
        // Визуальная обратная связь
        const copyBtn = document.querySelector('.copy-btn');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i>';
        copyBtn.style.background = '#00d4aa';
        
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.style.background = '';
        }, 2000);
        
        showNotification('Реферальная ссылка скопирована!', 'success');
    }
}

/**
 * Поиск по транзакциям
 */
function searchTransactions(query) {
    const transactions = document.querySelectorAll('.transaction-item');
    
    transactions.forEach(transaction => {
        const text = transaction.textContent.toLowerCase();
        const matches = text.includes(query.toLowerCase());
        transaction.style.display = matches ? 'flex' : 'none';
    });
}

/**
 * Фильтрация транзакций по типу
 */
function filterTransactions(type) {
    const transactions = document.querySelectorAll('.transaction-item');
    
    transactions.forEach(transaction => {
        const transactionType = transaction.querySelector('.transaction-type').textContent.toLowerCase();
        const matches = type === 'all' || transactionType.includes(type.toLowerCase());
        transaction.style.display = matches ? 'flex' : 'none';
    });
}

/**
 * Экспорт данных
 */
function exportData(type) {
    const url = `api/export.php?type=${type}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = `${type}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification(`Экспорт ${type} начат`, 'info');
}

/**
 * Обновление страницы без перезагрузки
 */
function refreshDashboard() {
    updateBalance();
    updateStats();
    loadNotifications();
    
    showNotification('Данные обновлены', 'success');
}

// Горячие клавиши
document.addEventListener('keydown', function(e) {
    // Ctrl + R - обновить данные
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        refreshDashboard();
    }
    
    // Ctrl + D - быстрое пополнение
    if (e.ctrlKey && e.key === 'd') {
        e.preventDefault();
        window.location.href = 'deposit.php';
    }
    
    // Ctrl + W - быстрый вывод
    if (e.ctrlKey && e.key === 'w') {
        e.preventDefault();
        window.location.href = 'withdraw.php';
    }
});

// Обработка видимости страницы
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        // Страница стала видимой - обновляем данные
        refreshDashboard();
    }
});
