<?php
/**
 * AstroGenix - Test Page
 * Страница для проверки работы сервера
 */

echo "<h1>AstroGenix - Тест сервера</h1>";
echo "<p>Сервер работает корректно!</p>";
echo "<p>PHP версия: " . phpversion() . "</p>";
echo "<p>Время сервера: " . date('Y-m-d H:i:s') . "</p>";

// Проверка подключения к базе данных
try {
    define('ASTROGENIX_INIT', true);
    require_once 'config.php';
    echo "<p style='color: green;'>✓ Подключение к базе данных успешно</p>";
    
    // Проверка таблиц
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Найдено таблиц в БД: " . count($tables) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Ошибка подключения к БД: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<a href='index.php'>← Вернуться на главную</a>";
?>
